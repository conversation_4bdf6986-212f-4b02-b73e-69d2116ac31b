{"name": "dntel-insurance", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "prebuild": "node scripts/generate-insurance-data.mjs", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "generate-data": "node scripts/generate-insurance-data.mjs"}, "dependencies": {"date-fns": "^4.1.0", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@faker-js/faker": "^9.9.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}
// Auto-generated insurance claims data
// Generated on: 2025-09-04T04:50:30.109Z
// Total records: 300

import { ClaimRowData } from '../components/ClaimRow';

export const insuranceClaimsData: ClaimRowData[] = [
  {
    "patient": {
      "name": "<PERSON>",
      "id": "PAT-53239"
    },
    "serviceDate": "05/09/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$7360.73",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "6:06 AM"
    },
    "user": "SW",
    "dateSent": "08/09/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Legros Family Practice",
      "id": "PROV-1646"
    }
  },
  {
    "patient": {
      "name": "<PERSON> Rempel",
      "id": "PAT-63279"
    },
    "serviceDate": "06/07/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$5279.06",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "8:28 PM"
    },
    "user": "SLT",
    "dateSent": "07/03/2025",
    "dateSentOrig": "07/03/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Lind Associates",
      "id": "PROV-4992"
    }
  },
  {
    "patient": {
      "name": "Daniel Pagac",
      "id": "PAT-10696"
    },
    "serviceDate": "05/13/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$6438.39",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "4:15 PM"
    },
    "user": "MNF",
    "dateSent": "07/22/2025",
    "dateSentOrig": "07/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Mills Associates",
      "id": "PROV-5759"
    }
  },
  {
    "patient": {
      "name": "Rosie Schulist",
      "id": "PAT-67663"
    },
    "serviceDate": "08/11/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$6450.25",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "1:46 PM"
    },
    "user": "MET",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Crona Family Practice",
      "id": "PROV-8229"
    }
  },
  {
    "patient": {
      "name": "Woodrow Medhurst",
      "id": "PAT-39256"
    },
    "serviceDate": "04/27/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$8737.65",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "12:16 PM"
    },
    "user": "AHG",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Hoppe Associates",
      "id": "PROV-4568"
    }
  },
  {
    "patient": {
      "name": "Joe Boyle",
      "id": "PAT-69395"
    },
    "serviceDate": "06/25/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$2319.68",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "10:54 AM"
    },
    "user": "MO",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kuhn Family Practice",
      "id": "PROV-8677"
    }
  },
  {
    "patient": {
      "name": "Thomas Langosh",
      "id": "PAT-33422"
    },
    "serviceDate": "09/02/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$10681.07",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "12:22 AM"
    },
    "user": "DK",
    "dateSent": "09/04/2025",
    "dateSentOrig": "09/03/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Walker Family Practice",
      "id": "PROV-5432"
    }
  },
  {
    "patient": {
      "name": "Lance Howe",
      "id": "PAT-97297"
    },
    "serviceDate": "05/12/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$5084.40",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "12:54 AM"
    },
    "user": "CKB",
    "dateSent": "06/06/2025",
    "dateSentOrig": "05/20/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Rempel Family Practice",
      "id": "PROV-4278"
    }
  },
  {
    "patient": {
      "name": "Rachael Flatley",
      "id": "PAT-21670"
    },
    "serviceDate": "04/21/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$6422.36",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "2:14 AM"
    },
    "user": "BEG",
    "dateSent": "05/18/2025",
    "dateSentOrig": "05/08/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Davis Clinic",
      "id": "PROV-4277"
    }
  },
  {
    "patient": {
      "name": "Cristina Schowalter",
      "id": "PAT-57750"
    },
    "serviceDate": "03/30/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$2938.39",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "3:56 PM"
    },
    "user": "JU",
    "dateSent": "09/01/2025",
    "dateSentOrig": "09/01/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Strosin Healthcare",
      "id": "PROV-6572"
    }
  },
  {
    "patient": {
      "name": "Margaret Ortiz",
      "id": "PAT-68612"
    },
    "serviceDate": "05/29/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$10039.02",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "9:37 PM"
    },
    "user": "CT",
    "dateSent": "05/31/2025",
    "dateSentOrig": "05/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Bernhard Associates",
      "id": "PROV-6830"
    }
  },
  {
    "patient": {
      "name": "Betty Luettgen",
      "id": "PAT-96153"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$14008.74",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "6:22 AM"
    },
    "user": "MQG",
    "dateSent": "07/07/2025",
    "dateSentOrig": "06/14/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Homenick Healthcare",
      "id": "PROV-6467"
    }
  },
  {
    "patient": {
      "name": "Luke Kovacek",
      "id": "PAT-76339"
    },
    "serviceDate": "08/03/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$3477.02",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "2:15 AM"
    },
    "user": "SBS",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hettinger Clinic",
      "id": "PROV-4102"
    }
  },
  {
    "patient": {
      "name": "Bobbie Hegmann",
      "id": "PAT-57776"
    },
    "serviceDate": "03/22/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$13220.79",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/04/2025",
      "time": "6:39 PM"
    },
    "user": "GL",
    "dateSent": "08/13/2025",
    "dateSentOrig": "04/16/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Hoeger Healthcare",
      "id": "PROV-8319"
    }
  },
  {
    "patient": {
      "name": "Israel Kuvalis",
      "id": "PAT-46737"
    },
    "serviceDate": "03/30/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$4588.89",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "3:04 AM"
    },
    "user": "CB",
    "dateSent": "05/05/2025",
    "dateSentOrig": "05/05/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Pollich Family Practice",
      "id": "PROV-8706"
    }
  },
  {
    "patient": {
      "name": "Claude Barton MD",
      "id": "PAT-21382"
    },
    "serviceDate": "07/04/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$4755.80",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "10:34 PM"
    },
    "user": "AT",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Kub-Mitchell Associates",
      "id": "PROV-2419"
    }
  },
  {
    "patient": {
      "name": "Felipe Champlin",
      "id": "PAT-20563"
    },
    "serviceDate": "05/27/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$8002.39",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "2:59 AM"
    },
    "user": "MF",
    "dateSent": "05/30/2025",
    "dateSentOrig": "05/30/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Stark Clinic",
      "id": "PROV-5492"
    }
  },
  {
    "patient": {
      "name": "Sidney Hermiston",
      "id": "PAT-75368"
    },
    "serviceDate": "04/18/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$13460.28",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "8:10 PM"
    },
    "user": "JB",
    "dateSent": "06/09/2025",
    "dateSentOrig": "06/09/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Lemke Healthcare",
      "id": "PROV-7186"
    }
  },
  {
    "patient": {
      "name": "Jimmie Crooks",
      "id": "PAT-64191"
    },
    "serviceDate": "07/06/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$9345.80",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "4:58 PM"
    },
    "user": "JM",
    "dateSent": "08/23/2025",
    "dateSentOrig": "07/11/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Dietrich Family Practice",
      "id": "PROV-2374"
    }
  },
  {
    "patient": {
      "name": "Emmett Armstrong",
      "id": "PAT-72167"
    },
    "serviceDate": "05/27/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$5023.30",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "8:39 AM"
    },
    "user": "RS",
    "dateSent": "07/03/2025",
    "dateSentOrig": "06/10/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Sanford Medical Group",
      "id": "PROV-2097"
    }
  },
  {
    "patient": {
      "name": "Rodolfo Ritchie",
      "id": "PAT-61887"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$12162.25",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "9:22 PM"
    },
    "user": "LKM",
    "dateSent": "08/27/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Wyman Medical Group",
      "id": "PROV-1678"
    }
  },
  {
    "patient": {
      "name": "Cassandra Fritsch",
      "id": "PAT-70982"
    },
    "serviceDate": "05/08/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$9312.96",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "3:56 PM"
    },
    "user": "RJM",
    "dateSent": "08/17/2025",
    "dateSentOrig": "07/01/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Raynor Clinic",
      "id": "PROV-1715"
    }
  },
  {
    "patient": {
      "name": "Ervin Luettgen Sr.",
      "id": "PAT-58010"
    },
    "serviceDate": "06/03/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$10060.72",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "9:04 PM"
    },
    "user": "NS",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Mitchell Associates",
      "id": "PROV-9538"
    }
  },
  {
    "patient": {
      "name": "Preston Rowe",
      "id": "PAT-60371"
    },
    "serviceDate": "05/30/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$3203.28",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "11:54 PM"
    },
    "user": "CM",
    "dateSent": "07/13/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Medhurst Healthcare",
      "id": "PROV-4015"
    }
  },
  {
    "patient": {
      "name": "Gretchen O'Connell",
      "id": "PAT-65815"
    },
    "serviceDate": "04/28/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$6274.61",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "3:03 AM"
    },
    "user": "NJS",
    "dateSent": "06/15/2025",
    "dateSentOrig": "06/15/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "McCullough Healthcare",
      "id": "PROV-6015"
    }
  },
  {
    "patient": {
      "name": "Rudolph Marquardt",
      "id": "PAT-20481"
    },
    "serviceDate": "07/24/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$10363.09",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "4:54 PM"
    },
    "user": "CCR",
    "dateSent": "08/15/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "West Medical Group",
      "id": "PROV-5857"
    }
  },
  {
    "patient": {
      "name": "Miranda Leuschke",
      "id": "PAT-74862"
    },
    "serviceDate": "07/01/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$9807.90",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "5:10 AM"
    },
    "user": "HN",
    "dateSent": "08/09/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Farrell Clinic",
      "id": "PROV-2874"
    }
  },
  {
    "patient": {
      "name": "Gwendolyn Satterfield",
      "id": "PAT-47763"
    },
    "serviceDate": "07/08/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$1870.94",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "12:05 PM"
    },
    "user": "AD",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Oberbrunner Clinic",
      "id": "PROV-8033"
    }
  },
  {
    "patient": {
      "name": "Alexis Hegmann",
      "id": "PAT-81298"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$6814.32",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "10:14 AM"
    },
    "user": "MED",
    "dateSent": "08/29/2025",
    "dateSentOrig": "07/21/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Windler Medical Group",
      "id": "PROV-7331"
    }
  },
  {
    "patient": {
      "name": "Cheryl Shanahan",
      "id": "PAT-62632"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$7062.75",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "4:40 PM"
    },
    "user": "JW",
    "dateSent": "08/02/2025",
    "dateSentOrig": "07/22/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kertzmann Healthcare",
      "id": "PROV-9776"
    }
  },
  {
    "patient": {
      "name": "Ms. Sadie Conroy PhD",
      "id": "PAT-50183"
    },
    "serviceDate": "08/19/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$3545.59",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "10:53 AM"
    },
    "user": "TRH",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hudson Associates",
      "id": "PROV-1552"
    }
  },
  {
    "patient": {
      "name": "Theresa Thiel",
      "id": "PAT-80745"
    },
    "serviceDate": "03/13/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$14014.48",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "12:29 PM"
    },
    "user": "LB",
    "dateSent": "05/05/2025",
    "dateSentOrig": "05/05/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Franey Medical Group",
      "id": "PROV-5199"
    }
  },
  {
    "patient": {
      "name": "Alejandro Bechtelar II",
      "id": "PAT-69860"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$4692.49",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "11:50 PM"
    },
    "user": "TRN",
    "dateSent": "06/09/2025",
    "dateSentOrig": "06/09/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Purdy Associates",
      "id": "PROV-5941"
    }
  },
  {
    "patient": {
      "name": "Bob Botsford IV",
      "id": "PAT-32418"
    },
    "serviceDate": "06/10/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$5689.37",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "8:28 AM"
    },
    "user": "BW",
    "dateSent": "08/11/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Pouros Associates",
      "id": "PROV-2196"
    }
  },
  {
    "patient": {
      "name": "Marsha Flatley",
      "id": "PAT-67047"
    },
    "serviceDate": "04/15/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$2035.58",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "3:20 AM"
    },
    "user": "HC",
    "dateSent": "08/05/2025",
    "dateSentOrig": "06/25/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Kris Clinic",
      "id": "PROV-3859"
    }
  },
  {
    "patient": {
      "name": "Loretta Schmidt",
      "id": "PAT-54052"
    },
    "serviceDate": "08/05/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$9353.78",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "9:07 AM"
    },
    "user": "SMY",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hartmann Associates",
      "id": "PROV-9807"
    }
  },
  {
    "patient": {
      "name": "Don Walsh",
      "id": "PAT-37235"
    },
    "serviceDate": "05/03/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$13341.77",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/04/2025",
      "time": "8:46 PM"
    },
    "user": "FR",
    "dateSent": "08/09/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Moore Healthcare",
      "id": "PROV-8014"
    }
  },
  {
    "patient": {
      "name": "Yvette Heller",
      "id": "PAT-44481"
    },
    "serviceDate": "04/07/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$7154.48",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "5:35 PM"
    },
    "user": "FK",
    "dateSent": "07/26/2025",
    "dateSentOrig": "06/02/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Veum Clinic",
      "id": "PROV-6546"
    }
  },
  {
    "patient": {
      "name": "Katrina Batz",
      "id": "PAT-73005"
    },
    "serviceDate": "08/09/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$5854.15",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "10:13 PM"
    },
    "user": "EY",
    "dateSent": "09/03/2025",
    "dateSentOrig": "09/03/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Breitenberg Medical Group",
      "id": "PROV-7715"
    }
  },
  {
    "patient": {
      "name": "Kim Bechtelar",
      "id": "PAT-22339"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$10641.34",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "11:51 AM"
    },
    "user": "HR",
    "dateSent": "09/03/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Treutel Clinic",
      "id": "PROV-8133"
    }
  },
  {
    "patient": {
      "name": "Dr. Maureen Pfeffer",
      "id": "PAT-47986"
    },
    "serviceDate": "06/19/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$11186.86",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "6:57 PM"
    },
    "user": "EG",
    "dateSent": "08/08/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Abernathy Family Practice",
      "id": "PROV-3184"
    }
  },
  {
    "patient": {
      "name": "Claudia Dickinson",
      "id": "PAT-11199"
    },
    "serviceDate": "05/07/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$13588.27",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "1:21 AM"
    },
    "user": "ORM",
    "dateSent": "07/27/2025",
    "dateSentOrig": "07/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Roob Clinic",
      "id": "PROV-7098"
    }
  },
  {
    "patient": {
      "name": "Susie Auer",
      "id": "PAT-15179"
    },
    "serviceDate": "06/02/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$6570.73",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "6:28 PM"
    },
    "user": "DNB",
    "dateSent": "08/17/2025",
    "dateSentOrig": "06/23/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "McLaughlin Healthcare",
      "id": "PROV-9141"
    }
  },
  {
    "patient": {
      "name": "Roland Kling",
      "id": "PAT-54720"
    },
    "serviceDate": "05/06/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$9318.80",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "5:28 PM"
    },
    "user": "ABW",
    "dateSent": "06/11/2025",
    "dateSentOrig": "06/11/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Mayer Family Practice",
      "id": "PROV-9777"
    }
  },
  {
    "patient": {
      "name": "Dr. Brittany Bernier",
      "id": "PAT-52914"
    },
    "serviceDate": "04/05/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$6721.97",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "1:23 AM"
    },
    "user": "HFZ",
    "dateSent": "08/02/2025",
    "dateSentOrig": "08/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Gerhold Medical Group",
      "id": "PROV-6515"
    }
  },
  {
    "patient": {
      "name": "Shirley Emard",
      "id": "PAT-22105"
    },
    "serviceDate": "06/22/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$4238.73",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "2:21 PM"
    },
    "user": "AGG",
    "dateSent": "07/28/2025",
    "dateSentOrig": "07/28/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Bednar Family Practice",
      "id": "PROV-6621"
    }
  },
  {
    "patient": {
      "name": "Tim Boehm-Weimann",
      "id": "PAT-31379"
    },
    "serviceDate": "07/16/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$13895.25",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "12:08 PM"
    },
    "user": "SSH",
    "dateSent": "07/24/2025",
    "dateSentOrig": "07/24/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kessler Healthcare",
      "id": "PROV-6876"
    }
  },
  {
    "patient": {
      "name": "Antonia Conroy DVM",
      "id": "PAT-94119"
    },
    "serviceDate": "03/16/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$2678.63",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "11:52 AM"
    },
    "user": "WG",
    "dateSent": "05/02/2025",
    "dateSentOrig": "04/16/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Collins Clinic",
      "id": "PROV-7974"
    }
  },
  {
    "patient": {
      "name": "Mr. Joseph Frami",
      "id": "PAT-31167"
    },
    "serviceDate": "06/19/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$10546.99",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "8:54 AM"
    },
    "user": "AAF",
    "dateSent": "08/05/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Bruen Associates",
      "id": "PROV-3597"
    }
  },
  {
    "patient": {
      "name": "Jacob Brekke",
      "id": "PAT-56896"
    },
    "serviceDate": "04/09/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$10205.12",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "3:14 PM"
    },
    "user": "RD",
    "dateSent": "07/14/2025",
    "dateSentOrig": "04/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Bosco Associates",
      "id": "PROV-3227"
    }
  },
  {
    "patient": {
      "name": "Bridget Block",
      "id": "PAT-48637"
    },
    "serviceDate": "05/31/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$4364.62",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "12:43 AM"
    },
    "user": "BRC",
    "dateSent": "07/13/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Herman Clinic",
      "id": "PROV-3749"
    }
  },
  {
    "patient": {
      "name": "Chelsea Boehm",
      "id": "PAT-75596"
    },
    "serviceDate": "05/04/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$9763.12",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "12:35 AM"
    },
    "user": "CL",
    "dateSent": "08/13/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "D'Amore Family Practice",
      "id": "PROV-5000"
    }
  },
  {
    "patient": {
      "name": "Marvin Wolff I",
      "id": "PAT-91949"
    },
    "serviceDate": "05/08/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$4035.67",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "3:06 PM"
    },
    "user": "GM",
    "dateSent": "08/06/2025",
    "dateSentOrig": "05/15/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "White Healthcare",
      "id": "PROV-3666"
    }
  },
  {
    "patient": {
      "name": "Laura Blick",
      "id": "PAT-90026"
    },
    "serviceDate": "08/08/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$5188.81",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "2:48 PM"
    },
    "user": "CL",
    "dateSent": "08/22/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Crist Medical Group",
      "id": "PROV-7100"
    }
  },
  {
    "patient": {
      "name": "Rebecca Franey",
      "id": "PAT-33981"
    },
    "serviceDate": "04/05/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$13092.66",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "1:42 AM"
    },
    "user": "ABM",
    "dateSent": "06/09/2025",
    "dateSentOrig": "06/09/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Stanton Associates",
      "id": "PROV-8683"
    }
  },
  {
    "patient": {
      "name": "Geoffrey Romaguera",
      "id": "PAT-13797"
    },
    "serviceDate": "06/18/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$10734.10",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "1:48 AM"
    },
    "user": "AM",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "White Medical Group",
      "id": "PROV-7978"
    }
  },
  {
    "patient": {
      "name": "Ms. Kim Prosacco IV",
      "id": "PAT-50810"
    },
    "serviceDate": "08/07/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$1370.04",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "6:18 PM"
    },
    "user": "WBR",
    "dateSent": "09/03/2025",
    "dateSentOrig": "09/03/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Steuber Associates",
      "id": "PROV-6921"
    }
  },
  {
    "patient": {
      "name": "Allan Bartell",
      "id": "PAT-80085"
    },
    "serviceDate": "06/09/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$5857.10",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "12:40 PM"
    },
    "user": "CFK",
    "dateSent": "08/10/2025",
    "dateSentOrig": "08/10/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Jacobi Medical Group",
      "id": "PROV-3862"
    }
  },
  {
    "patient": {
      "name": "Tim Heller",
      "id": "PAT-27072"
    },
    "serviceDate": "03/24/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$10794.31",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "2:34 AM"
    },
    "user": "JW",
    "dateSent": "08/05/2025",
    "dateSentOrig": "04/01/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Wehner Family Practice",
      "id": "PROV-7962"
    }
  },
  {
    "patient": {
      "name": "Florence Feil",
      "id": "PAT-44595"
    },
    "serviceDate": "04/22/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$2711.21",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "8:10 AM"
    },
    "user": "BS",
    "dateSent": "06/09/2025",
    "dateSentOrig": "06/04/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ward Family Practice",
      "id": "PROV-6340"
    }
  },
  {
    "patient": {
      "name": "Domingo Rohan",
      "id": "PAT-94841"
    },
    "serviceDate": "06/08/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$6837.80",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "1:57 AM"
    },
    "user": "BB",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/05/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Auer Clinic",
      "id": "PROV-6379"
    }
  },
  {
    "patient": {
      "name": "Randolph Kertzmann",
      "id": "PAT-21963"
    },
    "serviceDate": "07/04/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$5799.80",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "5:11 AM"
    },
    "user": "EKM",
    "dateSent": "07/20/2025",
    "dateSentOrig": "07/20/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Dickens Healthcare",
      "id": "PROV-7088"
    }
  },
  {
    "patient": {
      "name": "Dr. Ernestine VonRueden",
      "id": "PAT-65773"
    },
    "serviceDate": "05/05/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$8634.05",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "6:22 PM"
    },
    "user": "LK",
    "dateSent": "08/01/2025",
    "dateSentOrig": "05/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Davis Associates",
      "id": "PROV-3850"
    }
  },
  {
    "patient": {
      "name": "Ollie Walsh",
      "id": "PAT-24945"
    },
    "serviceDate": "07/20/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$14860.74",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "8:53 PM"
    },
    "user": "HS",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Prohaska Healthcare",
      "id": "PROV-2861"
    }
  },
  {
    "patient": {
      "name": "Raquel Heaney",
      "id": "PAT-25945"
    },
    "serviceDate": "06/28/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$10993.79",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "11:52 AM"
    },
    "user": "OK",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Feeney Family Practice",
      "id": "PROV-7469"
    }
  },
  {
    "patient": {
      "name": "Josephine Wolf",
      "id": "PAT-89604"
    },
    "serviceDate": "04/09/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$6200.93",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "11:10 AM"
    },
    "user": "LSL",
    "dateSent": "07/23/2025",
    "dateSentOrig": "07/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Kemmer Healthcare",
      "id": "PROV-7947"
    }
  },
  {
    "patient": {
      "name": "Kent Lind",
      "id": "PAT-55391"
    },
    "serviceDate": "05/27/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$14385.87",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "7:39 AM"
    },
    "user": "JS",
    "dateSent": "07/05/2025",
    "dateSentOrig": "07/05/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Becker Medical Group",
      "id": "PROV-2847"
    }
  },
  {
    "patient": {
      "name": "Teri Welch V",
      "id": "PAT-19391"
    },
    "serviceDate": "03/09/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$2626.83",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "7:23 AM"
    },
    "user": "ML",
    "dateSent": "04/12/2025",
    "dateSentOrig": "03/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Rath Clinic",
      "id": "PROV-3782"
    }
  },
  {
    "patient": {
      "name": "Eva Kris-Stokes",
      "id": "PAT-37185"
    },
    "serviceDate": "07/03/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$12241.98",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "3:38 AM"
    },
    "user": "LR",
    "dateSent": "07/29/2025",
    "dateSentOrig": "07/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Ritchie Medical Group",
      "id": "PROV-8347"
    }
  },
  {
    "patient": {
      "name": "Dr. Emilio Moore Sr.",
      "id": "PAT-49535"
    },
    "serviceDate": "09/01/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$9264.75",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "2:45 AM"
    },
    "user": "BK",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kirlin Healthcare",
      "id": "PROV-6485"
    }
  },
  {
    "patient": {
      "name": "Robyn Flatley",
      "id": "PAT-79912"
    },
    "serviceDate": "05/28/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$4888.31",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "4:15 PM"
    },
    "user": "VB",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Dickens Medical Group",
      "id": "PROV-7477"
    }
  },
  {
    "patient": {
      "name": "Mr. Shannon Rempel",
      "id": "PAT-73572"
    },
    "serviceDate": "05/25/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$9734.46",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "1:42 AM"
    },
    "user": "RM",
    "dateSent": "08/06/2025",
    "dateSentOrig": "06/25/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Zulauf Clinic",
      "id": "PROV-9406"
    }
  },
  {
    "patient": {
      "name": "Pablo Bergnaum",
      "id": "PAT-92011"
    },
    "serviceDate": "04/28/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$2429.73",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "3:11 PM"
    },
    "user": "VAH",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Baumbach Healthcare",
      "id": "PROV-5475"
    }
  },
  {
    "patient": {
      "name": "Byron Dicki",
      "id": "PAT-54773"
    },
    "serviceDate": "07/26/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$5510.39",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "2:05 PM"
    },
    "user": "AHL",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Quigley Associates",
      "id": "PROV-1904"
    }
  },
  {
    "patient": {
      "name": "Viola Mitchell Jr.",
      "id": "PAT-41608"
    },
    "serviceDate": "08/01/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$5550.96",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "12:38 PM"
    },
    "user": "PW",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kirlin Medical Group",
      "id": "PROV-5832"
    }
  },
  {
    "patient": {
      "name": "Paulette Price",
      "id": "PAT-95611"
    },
    "serviceDate": "03/20/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$9904.11",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "11:18 PM"
    },
    "user": "KSV",
    "dateSent": "08/11/2025",
    "dateSentOrig": "04/04/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Daniel-Weissnat Medical Group",
      "id": "PROV-8492"
    }
  },
  {
    "patient": {
      "name": "Philip Kilback",
      "id": "PAT-83251"
    },
    "serviceDate": "06/18/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$12721.72",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "12:29 AM"
    },
    "user": "LNE",
    "dateSent": "07/06/2025",
    "dateSentOrig": "07/06/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Weissnat Clinic",
      "id": "PROV-6239"
    }
  },
  {
    "patient": {
      "name": "Lamar Lowe",
      "id": "PAT-85879"
    },
    "serviceDate": "06/11/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$6704.29",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "12:54 PM"
    },
    "user": "EW",
    "dateSent": "08/24/2025",
    "dateSentOrig": "06/17/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Hackett Healthcare",
      "id": "PROV-6543"
    }
  },
  {
    "patient": {
      "name": "Alyssa Kuhic",
      "id": "PAT-71953"
    },
    "serviceDate": "08/19/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$8857.14",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "8:49 AM"
    },
    "user": "JDB",
    "dateSent": "09/04/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Legros Family Practice",
      "id": "PROV-5150"
    }
  },
  {
    "patient": {
      "name": "Ann Brown",
      "id": "PAT-78436"
    },
    "serviceDate": "04/06/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$3081.64",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "6:31 PM"
    },
    "user": "BJL",
    "dateSent": "07/19/2025",
    "dateSentOrig": "07/19/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Wisozk Associates",
      "id": "PROV-1104"
    }
  },
  {
    "patient": {
      "name": "Ervin Runolfsson",
      "id": "PAT-71115"
    },
    "serviceDate": "03/30/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$4176.41",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "6:52 AM"
    },
    "user": "ANL",
    "dateSent": "06/23/2025",
    "dateSentOrig": "06/23/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Auer Family Practice",
      "id": "PROV-2201"
    }
  },
  {
    "patient": {
      "name": "Desiree Bradtke",
      "id": "PAT-31099"
    },
    "serviceDate": "06/14/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$4849.62",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "5:31 AM"
    },
    "user": "JJ",
    "dateSent": "07/22/2025",
    "dateSentOrig": "07/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Larson Family Practice",
      "id": "PROV-5824"
    }
  },
  {
    "patient": {
      "name": "Kimberly Barrows",
      "id": "PAT-13512"
    },
    "serviceDate": "07/08/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$12309.40",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "1:20 AM"
    },
    "user": "KHS",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Flatley Medical Group",
      "id": "PROV-9615"
    }
  },
  {
    "patient": {
      "name": "Mr. Toby Corkery",
      "id": "PAT-86801"
    },
    "serviceDate": "07/01/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$7639.85",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "10:17 AM"
    },
    "user": "KB",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/19/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Sipes-O'Hara Clinic",
      "id": "PROV-9495"
    }
  },
  {
    "patient": {
      "name": "Olga Leuschke",
      "id": "PAT-93573"
    },
    "serviceDate": "03/20/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$7720.13",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "2:48 PM"
    },
    "user": "BN",
    "dateSent": "08/08/2025",
    "dateSentOrig": "05/21/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Haag Family Practice",
      "id": "PROV-4694"
    }
  },
  {
    "patient": {
      "name": "Sonia Schmitt-Abshire",
      "id": "PAT-73579"
    },
    "serviceDate": "04/08/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$1652.15",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "1:31 PM"
    },
    "user": "EB",
    "dateSent": "07/15/2025",
    "dateSentOrig": "05/11/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Kovacek Family Practice",
      "id": "PROV-2536"
    }
  },
  {
    "patient": {
      "name": "Charlie McDermott",
      "id": "PAT-81632"
    },
    "serviceDate": "03/26/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$12328.83",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "8:19 AM"
    },
    "user": "RR",
    "dateSent": "04/22/2025",
    "dateSentOrig": "04/10/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Hilll Family Practice",
      "id": "PROV-6947"
    }
  },
  {
    "patient": {
      "name": "Betty Zieme",
      "id": "PAT-63421"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$13490.27",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "8:07 PM"
    },
    "user": "BW",
    "dateSent": "07/08/2025",
    "dateSentOrig": "05/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Romaguera Associates",
      "id": "PROV-7139"
    }
  },
  {
    "patient": {
      "name": "Guadalupe Quitzon",
      "id": "PAT-99144"
    },
    "serviceDate": "06/21/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$3173.25",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "5:18 AM"
    },
    "user": "LRC",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Simonis Family Practice",
      "id": "PROV-8345"
    }
  },
  {
    "patient": {
      "name": "Mr. Enrique Brekke",
      "id": "PAT-92709"
    },
    "serviceDate": "03/29/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$11247.64",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "12:08 AM"
    },
    "user": "LEL",
    "dateSent": "07/04/2025",
    "dateSentOrig": "07/04/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Wintheiser Medical Group",
      "id": "PROV-9527"
    }
  },
  {
    "patient": {
      "name": "Earl Collins",
      "id": "PAT-49443"
    },
    "serviceDate": "04/20/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$10666.51",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "1:32 AM"
    },
    "user": "JW",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Terry Family Practice",
      "id": "PROV-5178"
    }
  },
  {
    "patient": {
      "name": "Erma Hyatt",
      "id": "PAT-41520"
    },
    "serviceDate": "08/25/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$5316.97",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "10:21 PM"
    },
    "user": "BSR",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Dooley Healthcare",
      "id": "PROV-4516"
    }
  },
  {
    "patient": {
      "name": "Jody Kris",
      "id": "PAT-87727"
    },
    "serviceDate": "05/07/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$11445.98",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "10:13 AM"
    },
    "user": "SPC",
    "dateSent": "07/20/2025",
    "dateSentOrig": "07/20/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "McDermott Family Practice",
      "id": "PROV-8363"
    }
  },
  {
    "patient": {
      "name": "Margie Okuneva",
      "id": "PAT-41810"
    },
    "serviceDate": "03/29/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$14385.24",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "1:51 PM"
    },
    "user": "MAG",
    "dateSent": "06/10/2025",
    "dateSentOrig": "06/10/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Mayert Associates",
      "id": "PROV-2788"
    }
  },
  {
    "patient": {
      "name": "Veronica Bashirian-Daugherty IV",
      "id": "PAT-54689"
    },
    "serviceDate": "08/08/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$543.02",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "1:35 PM"
    },
    "user": "VKM",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Gerlach Healthcare",
      "id": "PROV-1744"
    }
  },
  {
    "patient": {
      "name": "Julia O'Connell",
      "id": "PAT-22410"
    },
    "serviceDate": "08/12/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$11040.89",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/04/2025",
      "time": "12:04 AM"
    },
    "user": "LRG",
    "dateSent": "09/03/2025",
    "dateSentOrig": "08/19/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Witting Healthcare",
      "id": "PROV-2177"
    }
  },
  {
    "patient": {
      "name": "Myron Toy",
      "id": "PAT-80839"
    },
    "serviceDate": "03/11/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$11911.69",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "12:09 PM"
    },
    "user": "KH",
    "dateSent": "09/03/2025",
    "dateSentOrig": "09/03/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Mosciski Medical Group",
      "id": "PROV-1221"
    }
  },
  {
    "patient": {
      "name": "Doreen Dibbert DVM",
      "id": "PAT-26032"
    },
    "serviceDate": "05/28/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$1961.13",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "4:16 AM"
    },
    "user": "AH",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Schaden Healthcare",
      "id": "PROV-1021"
    }
  },
  {
    "patient": {
      "name": "Kristine Buckridge",
      "id": "PAT-18498"
    },
    "serviceDate": "04/28/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$9646.46",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "4:12 PM"
    },
    "user": "VBD",
    "dateSent": "06/07/2025",
    "dateSentOrig": "05/01/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Wisozk Medical Group",
      "id": "PROV-5220"
    }
  },
  {
    "patient": {
      "name": "Gregory Barton-Schiller IV",
      "id": "PAT-95503"
    },
    "serviceDate": "08/07/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$2111.23",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "1:54 PM"
    },
    "user": "BK",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Kuhic Associates",
      "id": "PROV-4600"
    }
  },
  {
    "patient": {
      "name": "Fannie Gerhold-Goyette",
      "id": "PAT-89003"
    },
    "serviceDate": "06/13/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$14706.55",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "11:47 PM"
    },
    "user": "ABD",
    "dateSent": "07/22/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hintz Healthcare",
      "id": "PROV-8503"
    }
  },
  {
    "patient": {
      "name": "Dr. Curtis Thompson",
      "id": "PAT-66136"
    },
    "serviceDate": "06/09/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$5067.91",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "3:49 PM"
    },
    "user": "KH",
    "dateSent": "08/03/2025",
    "dateSentOrig": "08/03/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Schumm Family Practice",
      "id": "PROV-5651"
    }
  },
  {
    "patient": {
      "name": "Angel Bradtke I",
      "id": "PAT-37474"
    },
    "serviceDate": "03/20/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$5578.09",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "5:17 PM"
    },
    "user": "JM",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Bradtke Family Practice",
      "id": "PROV-3448"
    }
  },
  {
    "patient": {
      "name": "Ronald Schumm",
      "id": "PAT-41191"
    },
    "serviceDate": "04/04/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$11678.15",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "3:14 AM"
    },
    "user": "IW",
    "dateSent": "06/12/2025",
    "dateSentOrig": "05/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Cronin Associates",
      "id": "PROV-3207"
    }
  },
  {
    "patient": {
      "name": "Mr. Colin Bins II",
      "id": "PAT-49327"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$5054.80",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "2:15 AM"
    },
    "user": "DSD",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Braun Associates",
      "id": "PROV-7679"
    }
  },
  {
    "patient": {
      "name": "Troy McCullough",
      "id": "PAT-10069"
    },
    "serviceDate": "06/24/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$11753.31",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "10:56 PM"
    },
    "user": "KC",
    "dateSent": "08/26/2025",
    "dateSentOrig": "07/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Hyatt-Brown Medical Group",
      "id": "PROV-6412"
    }
  },
  {
    "patient": {
      "name": "Zachary Hills",
      "id": "PAT-21804"
    },
    "serviceDate": "06/18/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$9329.97",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "10:51 PM"
    },
    "user": "CBB",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Conn Healthcare",
      "id": "PROV-5687"
    }
  },
  {
    "patient": {
      "name": "Jody Hahn",
      "id": "PAT-49746"
    },
    "serviceDate": "06/28/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$6178.33",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "8:25 AM"
    },
    "user": "PB",
    "dateSent": "07/29/2025",
    "dateSentOrig": "07/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Halvorson Clinic",
      "id": "PROV-5374"
    }
  },
  {
    "patient": {
      "name": "Gustavo Nolan",
      "id": "PAT-47662"
    },
    "serviceDate": "05/30/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$12924.88",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "5:59 PM"
    },
    "user": "MH",
    "dateSent": "07/26/2025",
    "dateSentOrig": "07/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Feil Family Practice",
      "id": "PROV-1052"
    }
  },
  {
    "patient": {
      "name": "Dexter Schulist",
      "id": "PAT-56122"
    },
    "serviceDate": "04/08/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$3106.91",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "3:30 PM"
    },
    "user": "CBF",
    "dateSent": "06/05/2025",
    "dateSentOrig": "05/12/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Rogahn Family Practice",
      "id": "PROV-3365"
    }
  },
  {
    "patient": {
      "name": "Ruby Treutel",
      "id": "PAT-46757"
    },
    "serviceDate": "05/31/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$14474.89",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "4:29 PM"
    },
    "user": "EJ",
    "dateSent": "07/16/2025",
    "dateSentOrig": "06/08/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Baumbach Healthcare",
      "id": "PROV-9047"
    }
  },
  {
    "patient": {
      "name": "Candice Sawayn",
      "id": "PAT-26082"
    },
    "serviceDate": "07/20/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$11012.17",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "6:11 PM"
    },
    "user": "FTK",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Johnston Medical Group",
      "id": "PROV-7908"
    }
  },
  {
    "patient": {
      "name": "Dr. Marlene Olson",
      "id": "PAT-59949"
    },
    "serviceDate": "07/21/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$9370.11",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "9:22 AM"
    },
    "user": "LMV",
    "dateSent": "08/17/2025",
    "dateSentOrig": "07/23/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Connelly Associates",
      "id": "PROV-8058"
    }
  },
  {
    "patient": {
      "name": "Margarita Barton",
      "id": "PAT-66122"
    },
    "serviceDate": "05/03/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$4850.21",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "12:34 PM"
    },
    "user": "WM",
    "dateSent": "08/01/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Hodkiewicz Medical Group",
      "id": "PROV-7874"
    }
  },
  {
    "patient": {
      "name": "Ms. Ada O'Connell",
      "id": "PAT-14758"
    },
    "serviceDate": "07/30/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$11691.63",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "3:26 AM"
    },
    "user": "JC",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Roob Healthcare",
      "id": "PROV-9584"
    }
  },
  {
    "patient": {
      "name": "Patsy Gleichner",
      "id": "PAT-29067"
    },
    "serviceDate": "06/24/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$5354.32",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "7:00 PM"
    },
    "user": "EB",
    "dateSent": "08/13/2025",
    "dateSentOrig": "07/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Walter Family Practice",
      "id": "PROV-8052"
    }
  },
  {
    "patient": {
      "name": "Lonnie Jacobs",
      "id": "PAT-50571"
    },
    "serviceDate": "06/03/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$3974.71",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "4:07 PM"
    },
    "user": "JCD",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/19/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Schowalter Associates",
      "id": "PROV-4587"
    }
  },
  {
    "patient": {
      "name": "Dr. Stewart Hintz-Terry",
      "id": "PAT-62333"
    },
    "serviceDate": "08/11/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$13078.53",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "1:31 AM"
    },
    "user": "BCL",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Feil Family Practice",
      "id": "PROV-6253"
    }
  },
  {
    "patient": {
      "name": "Alberta Cormier",
      "id": "PAT-50046"
    },
    "serviceDate": "07/10/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$14589.57",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "10:58 AM"
    },
    "user": "JG",
    "dateSent": "07/22/2025",
    "dateSentOrig": "07/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Daugherty Healthcare",
      "id": "PROV-1229"
    }
  },
  {
    "patient": {
      "name": "Shaun Dach",
      "id": "PAT-25908"
    },
    "serviceDate": "08/14/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$282.50",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "11:36 AM"
    },
    "user": "ASM",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Ziemann Healthcare",
      "id": "PROV-9201"
    }
  },
  {
    "patient": {
      "name": "Dr. Ellen Larson",
      "id": "PAT-34045"
    },
    "serviceDate": "08/17/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$4310.91",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "12:31 AM"
    },
    "user": "GRR",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Gulgowski Family Practice",
      "id": "PROV-3249"
    }
  },
  {
    "patient": {
      "name": "Doyle Hagenes",
      "id": "PAT-46983"
    },
    "serviceDate": "08/27/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$7006.91",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "6:26 AM"
    },
    "user": "DH",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Cassin Clinic",
      "id": "PROV-9433"
    }
  },
  {
    "patient": {
      "name": "Dr. Charlie Goyette",
      "id": "PAT-91020"
    },
    "serviceDate": "04/19/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$14966.91",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "12:21 AM"
    },
    "user": "EHW",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Cruickshank Medical Group",
      "id": "PROV-9063"
    }
  },
  {
    "patient": {
      "name": "Darrel Rodriguez V",
      "id": "PAT-22885"
    },
    "serviceDate": "06/21/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$3305.45",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "7:07 AM"
    },
    "user": "DST",
    "dateSent": "07/13/2025",
    "dateSentOrig": "06/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Dickens Family Practice",
      "id": "PROV-2664"
    }
  },
  {
    "patient": {
      "name": "Myron Hackett DDS",
      "id": "PAT-64313"
    },
    "serviceDate": "04/27/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$9266.32",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "9:01 AM"
    },
    "user": "AH",
    "dateSent": "06/16/2025",
    "dateSentOrig": "05/14/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Swift Associates",
      "id": "PROV-2268"
    }
  },
  {
    "patient": {
      "name": "Lydia Reilly",
      "id": "PAT-98312"
    },
    "serviceDate": "03/18/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$2092.10",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "4:08 AM"
    },
    "user": "GDM",
    "dateSent": "06/02/2025",
    "dateSentOrig": "06/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Hagenes Clinic",
      "id": "PROV-2916"
    }
  },
  {
    "patient": {
      "name": "Dr. Constance Hirthe",
      "id": "PAT-34287"
    },
    "serviceDate": "07/30/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$237.27",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "9:23 PM"
    },
    "user": "DKD",
    "dateSent": "07/31/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Will Clinic",
      "id": "PROV-9226"
    }
  },
  {
    "patient": {
      "name": "Lindsay Runolfsson MD",
      "id": "PAT-88643"
    },
    "serviceDate": "03/20/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$8384.84",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "7:19 AM"
    },
    "user": "SS",
    "dateSent": "07/20/2025",
    "dateSentOrig": "05/02/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Koss Medical Group",
      "id": "PROV-7758"
    }
  },
  {
    "patient": {
      "name": "Bruce Schoen",
      "id": "PAT-24920"
    },
    "serviceDate": "07/05/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$6763.89",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "8:40 AM"
    },
    "user": "JRM",
    "dateSent": "07/29/2025",
    "dateSentOrig": "07/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Satterfield Clinic",
      "id": "PROV-4183"
    }
  },
  {
    "patient": {
      "name": "Shelia Langosh DDS",
      "id": "PAT-51439"
    },
    "serviceDate": "06/08/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$8398.07",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "7:33 AM"
    },
    "user": "MB",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Kautzer Medical Group",
      "id": "PROV-4853"
    }
  },
  {
    "patient": {
      "name": "Miss Beverly McCullough-Stokes IV",
      "id": "PAT-51506"
    },
    "serviceDate": "04/17/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$8427.23",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "1:58 PM"
    },
    "user": "KRK",
    "dateSent": "08/04/2025",
    "dateSentOrig": "08/04/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Braun Associates",
      "id": "PROV-2637"
    }
  },
  {
    "patient": {
      "name": "Emmett Kub",
      "id": "PAT-88480"
    },
    "serviceDate": "08/17/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$3792.19",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "1:38 AM"
    },
    "user": "VK",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Blanda Healthcare",
      "id": "PROV-4495"
    }
  },
  {
    "patient": {
      "name": "Lucille Moen",
      "id": "PAT-94794"
    },
    "serviceDate": "05/18/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$5087.10",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "3:20 AM"
    },
    "user": "BS",
    "dateSent": "07/31/2025",
    "dateSentOrig": "06/13/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Mayer Family Practice",
      "id": "PROV-7988"
    }
  },
  {
    "patient": {
      "name": "Garry Johnston",
      "id": "PAT-76343"
    },
    "serviceDate": "07/11/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$12231.15",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "12:35 AM"
    },
    "user": "DS",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Casper Clinic",
      "id": "PROV-6159"
    }
  },
  {
    "patient": {
      "name": "Clay Dickens",
      "id": "PAT-90920"
    },
    "serviceDate": "06/12/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$8930.34",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "6:25 PM"
    },
    "user": "PRP",
    "dateSent": "07/09/2025",
    "dateSentOrig": "07/06/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Cremin Associates",
      "id": "PROV-4208"
    }
  },
  {
    "patient": {
      "name": "Felix Langworth",
      "id": "PAT-76103"
    },
    "serviceDate": "06/23/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$10972.04",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "10:40 PM"
    },
    "user": "JW",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Langworth Family Practice",
      "id": "PROV-7865"
    }
  },
  {
    "patient": {
      "name": "Juan Aufderhar PhD",
      "id": "PAT-78109"
    },
    "serviceDate": "05/17/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$8536.73",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "11:03 PM"
    },
    "user": "BHB",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Cummings Healthcare",
      "id": "PROV-6711"
    }
  },
  {
    "patient": {
      "name": "Kendra Trantow",
      "id": "PAT-15137"
    },
    "serviceDate": "09/04/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$11700.46",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "6:20 PM"
    },
    "user": "MK",
    "dateSent": "09/04/2025",
    "dateSentOrig": "09/04/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Crona Associates",
      "id": "PROV-2882"
    }
  },
  {
    "patient": {
      "name": "Jesse Klocko-Romaguera",
      "id": "PAT-65104"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$3762.04",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "1:44 PM"
    },
    "user": "WS",
    "dateSent": "08/01/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Mohr-Wilkinson Family Practice",
      "id": "PROV-1538"
    }
  },
  {
    "patient": {
      "name": "Alvin Treutel",
      "id": "PAT-35425"
    },
    "serviceDate": "04/04/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$13714.20",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "12:34 PM"
    },
    "user": "ANG",
    "dateSent": "07/27/2025",
    "dateSentOrig": "07/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hansen Associates",
      "id": "PROV-6423"
    }
  },
  {
    "patient": {
      "name": "Archie Torp",
      "id": "PAT-80972"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$5916.31",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "5:39 PM"
    },
    "user": "ASQ",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Pacocha Family Practice",
      "id": "PROV-5695"
    }
  },
  {
    "patient": {
      "name": "Lynne Howe",
      "id": "PAT-41779"
    },
    "serviceDate": "06/14/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$1235.96",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "3:49 PM"
    },
    "user": "VU",
    "dateSent": "07/25/2025",
    "dateSentOrig": "07/16/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Doyle Medical Group",
      "id": "PROV-3844"
    }
  },
  {
    "patient": {
      "name": "Edgar Wehner",
      "id": "PAT-67347"
    },
    "serviceDate": "08/16/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$8614.47",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "3:33 PM"
    },
    "user": "JM",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schiller Clinic",
      "id": "PROV-6393"
    }
  },
  {
    "patient": {
      "name": "Ruben Jacobi",
      "id": "PAT-98077"
    },
    "serviceDate": "03/29/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$6324.48",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "1:22 PM"
    },
    "user": "KK",
    "dateSent": "07/01/2025",
    "dateSentOrig": "06/03/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Marquardt Clinic",
      "id": "PROV-4296"
    }
  },
  {
    "patient": {
      "name": "Cameron Russel",
      "id": "PAT-96512"
    },
    "serviceDate": "06/23/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$7660.84",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "2:08 AM"
    },
    "user": "JAH",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kerluke Associates",
      "id": "PROV-3139"
    }
  },
  {
    "patient": {
      "name": "Sara MacGyver",
      "id": "PAT-28452"
    },
    "serviceDate": "04/05/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$2246.10",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "9:52 PM"
    },
    "user": "GS",
    "dateSent": "04/28/2025",
    "dateSentOrig": "04/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Schamberger Family Practice",
      "id": "PROV-8598"
    }
  },
  {
    "patient": {
      "name": "Rafael Wolf",
      "id": "PAT-57973"
    },
    "serviceDate": "03/28/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$962.75",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "3:16 AM"
    },
    "user": "GH",
    "dateSent": "06/09/2025",
    "dateSentOrig": "06/09/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Roob Family Practice",
      "id": "PROV-7136"
    }
  },
  {
    "patient": {
      "name": "Ms. Tabitha Effertz",
      "id": "PAT-45944"
    },
    "serviceDate": "04/06/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$5368.02",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "1:05 PM"
    },
    "user": "SH",
    "dateSent": "07/28/2025",
    "dateSentOrig": "05/26/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Strosin Clinic",
      "id": "PROV-5446"
    }
  },
  {
    "patient": {
      "name": "Jacob Skiles",
      "id": "PAT-72026"
    },
    "serviceDate": "07/30/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$201.90",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "3:17 PM"
    },
    "user": "LLB",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Lind Medical Group",
      "id": "PROV-2521"
    }
  },
  {
    "patient": {
      "name": "Shawn Schaden",
      "id": "PAT-27249"
    },
    "serviceDate": "07/16/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$9260.87",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "12:22 PM"
    },
    "user": "MAS",
    "dateSent": "07/29/2025",
    "dateSentOrig": "07/29/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Graham-Bauch Healthcare",
      "id": "PROV-9135"
    }
  },
  {
    "patient": {
      "name": "Antonio Dooley",
      "id": "PAT-41028"
    },
    "serviceDate": "06/17/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$4851.39",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "3:55 AM"
    },
    "user": "NZ",
    "dateSent": "06/30/2025",
    "dateSentOrig": "06/30/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Huel Associates",
      "id": "PROV-7671"
    }
  },
  {
    "patient": {
      "name": "Dora Kuvalis",
      "id": "PAT-99091"
    },
    "serviceDate": "08/02/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$12789.69",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "5:04 PM"
    },
    "user": "GG",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/05/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Russel Healthcare",
      "id": "PROV-7439"
    }
  },
  {
    "patient": {
      "name": "Clinton Jones",
      "id": "PAT-13666"
    },
    "serviceDate": "07/30/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$12729.90",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "9:33 AM"
    },
    "user": "TF",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/11/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Collier Medical Group",
      "id": "PROV-1925"
    }
  },
  {
    "patient": {
      "name": "Ricky Strosin",
      "id": "PAT-73850"
    },
    "serviceDate": "06/17/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$6960.23",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "2:08 AM"
    },
    "user": "JB",
    "dateSent": "07/24/2025",
    "dateSentOrig": "07/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Hickle Clinic",
      "id": "PROV-7558"
    }
  },
  {
    "patient": {
      "name": "Lorene Luettgen",
      "id": "PAT-17867"
    },
    "serviceDate": "07/28/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$1518.44",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "11:50 AM"
    },
    "user": "JW",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Mante Medical Group",
      "id": "PROV-2625"
    }
  },
  {
    "patient": {
      "name": "Leah Goldner MD",
      "id": "PAT-89433"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$2383.93",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "2:56 AM"
    },
    "user": "NA",
    "dateSent": "06/08/2025",
    "dateSentOrig": "06/08/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Heidenreich Medical Group",
      "id": "PROV-6747"
    }
  },
  {
    "patient": {
      "name": "Ernestine Kutch",
      "id": "PAT-62575"
    },
    "serviceDate": "08/30/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$6116.91",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "10:35 AM"
    },
    "user": "KF",
    "dateSent": "09/03/2025",
    "dateSentOrig": "09/03/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Fritsch Associates",
      "id": "PROV-3907"
    }
  },
  {
    "patient": {
      "name": "Jody Kling",
      "id": "PAT-62695"
    },
    "serviceDate": "03/10/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$10323.33",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "5:50 PM"
    },
    "user": "DCA",
    "dateSent": "05/18/2025",
    "dateSentOrig": "05/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Wilkinson Healthcare",
      "id": "PROV-3677"
    }
  },
  {
    "patient": {
      "name": "Jeremy Powlowski",
      "id": "PAT-12200"
    },
    "serviceDate": "08/06/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$7062.18",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "7:10 AM"
    },
    "user": "HRR",
    "dateSent": "08/07/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Rogahn Associates",
      "id": "PROV-3517"
    }
  },
  {
    "patient": {
      "name": "Sherman Schoen",
      "id": "PAT-87009"
    },
    "serviceDate": "08/17/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$1257.63",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "3:02 AM"
    },
    "user": "GB",
    "dateSent": "09/04/2025",
    "dateSentOrig": "09/04/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Paucek Healthcare",
      "id": "PROV-1142"
    }
  },
  {
    "patient": {
      "name": "Yvette Kassulke",
      "id": "PAT-44892"
    },
    "serviceDate": "05/11/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$12965.63",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "1:38 AM"
    },
    "user": "ECR",
    "dateSent": "07/28/2025",
    "dateSentOrig": "05/21/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kertzmann Associates",
      "id": "PROV-7709"
    }
  },
  {
    "patient": {
      "name": "Dr. Fredrick Franey",
      "id": "PAT-64657"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$1851.80",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "7:45 PM"
    },
    "user": "SS",
    "dateSent": "06/02/2025",
    "dateSentOrig": "06/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Nitzsche Clinic",
      "id": "PROV-8692"
    }
  },
  {
    "patient": {
      "name": "Jessie McCullough",
      "id": "PAT-44503"
    },
    "serviceDate": "08/13/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$3365.42",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "12:44 PM"
    },
    "user": "TC",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Shields-Witting Medical Group",
      "id": "PROV-3383"
    }
  },
  {
    "patient": {
      "name": "Dr. Kristopher Moen",
      "id": "PAT-38106"
    },
    "serviceDate": "05/10/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$11409.14",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/04/2025",
      "time": "9:33 AM"
    },
    "user": "BBS",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Streich Healthcare",
      "id": "PROV-6537"
    }
  },
  {
    "patient": {
      "name": "Jill Donnelly MD",
      "id": "PAT-35427"
    },
    "serviceDate": "04/15/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$14905.13",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "11:27 AM"
    },
    "user": "RP",
    "dateSent": "07/06/2025",
    "dateSentOrig": "04/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Schamberger Healthcare",
      "id": "PROV-8802"
    }
  },
  {
    "patient": {
      "name": "Howard Mante-Wisozk",
      "id": "PAT-53488"
    },
    "serviceDate": "03/31/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$6413.98",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "9:07 PM"
    },
    "user": "TH",
    "dateSent": "04/01/2025",
    "dateSentOrig": "04/01/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Walter Medical Group",
      "id": "PROV-1673"
    }
  },
  {
    "patient": {
      "name": "Diana Tromp",
      "id": "PAT-66419"
    },
    "serviceDate": "05/27/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$5433.93",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/04/2025",
      "time": "4:23 PM"
    },
    "user": "TNK",
    "dateSent": "07/06/2025",
    "dateSentOrig": "07/06/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Fahey Medical Group",
      "id": "PROV-6010"
    }
  },
  {
    "patient": {
      "name": "Johnny Vandervort",
      "id": "PAT-28558"
    },
    "serviceDate": "03/14/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$5109.68",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "2:44 AM"
    },
    "user": "NS",
    "dateSent": "04/26/2025",
    "dateSentOrig": "04/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "McCullough Clinic",
      "id": "PROV-1737"
    }
  },
  {
    "patient": {
      "name": "Mr. Roland Raynor",
      "id": "PAT-88810"
    },
    "serviceDate": "06/13/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$9178.05",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "4:44 PM"
    },
    "user": "GK",
    "dateSent": "06/24/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Zulauf Associates",
      "id": "PROV-4188"
    }
  },
  {
    "patient": {
      "name": "Tony Kessler",
      "id": "PAT-70463"
    },
    "serviceDate": "06/28/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$3793.95",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "8:16 PM"
    },
    "user": "EAT",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/03/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Johns Healthcare",
      "id": "PROV-6365"
    }
  },
  {
    "patient": {
      "name": "Emilio Turner",
      "id": "PAT-62348"
    },
    "serviceDate": "08/06/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$9393.41",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "12:58 PM"
    },
    "user": "JD",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hegmann Associates",
      "id": "PROV-5427"
    }
  },
  {
    "patient": {
      "name": "Mr. Andy Rowe-Mosciski",
      "id": "PAT-86683"
    },
    "serviceDate": "06/22/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$1428.50",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "11:29 AM"
    },
    "user": "MJM",
    "dateSent": "07/28/2025",
    "dateSentOrig": "07/21/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Dibbert Healthcare",
      "id": "PROV-7962"
    }
  },
  {
    "patient": {
      "name": "Jermaine Bernier",
      "id": "PAT-16043"
    },
    "serviceDate": "08/13/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$13774.62",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "7:44 AM"
    },
    "user": "EL",
    "dateSent": "08/15/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Wilkinson Medical Group",
      "id": "PROV-7066"
    }
  },
  {
    "patient": {
      "name": "Albert Walsh",
      "id": "PAT-28011"
    },
    "serviceDate": "05/11/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$13338.10",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "7:31 PM"
    },
    "user": "CD",
    "dateSent": "05/16/2025",
    "dateSentOrig": "05/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Morissette Medical Group",
      "id": "PROV-5398"
    }
  },
  {
    "patient": {
      "name": "Andrea Cummerata",
      "id": "PAT-63618"
    },
    "serviceDate": "08/05/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$2138.90",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "4:06 PM"
    },
    "user": "MRT",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/05/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Tillman Associates",
      "id": "PROV-2756"
    }
  },
  {
    "patient": {
      "name": "Wendell Dibbert Sr.",
      "id": "PAT-66819"
    },
    "serviceDate": "08/04/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$3470.41",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "2:50 PM"
    },
    "user": "MH",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Satterfield Medical Group",
      "id": "PROV-5497"
    }
  },
  {
    "patient": {
      "name": "Tyrone Beatty",
      "id": "PAT-60789"
    },
    "serviceDate": "07/04/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$5987.11",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "10:19 AM"
    },
    "user": "DH",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Armstrong Clinic",
      "id": "PROV-9426"
    }
  },
  {
    "patient": {
      "name": "Alfred Wolf",
      "id": "PAT-77104"
    },
    "serviceDate": "07/04/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$3992.67",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "1:04 PM"
    },
    "user": "TM",
    "dateSent": "09/01/2025",
    "dateSentOrig": "07/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Swift Healthcare",
      "id": "PROV-2459"
    }
  },
  {
    "patient": {
      "name": "Allan Schuster",
      "id": "PAT-86268"
    },
    "serviceDate": "05/02/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$7957.74",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "8:09 PM"
    },
    "user": "RB",
    "dateSent": "05/25/2025",
    "dateSentOrig": "05/07/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Mraz Healthcare",
      "id": "PROV-9183"
    }
  },
  {
    "patient": {
      "name": "Austin Kutch",
      "id": "PAT-26295"
    },
    "serviceDate": "04/22/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$7249.68",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "10:54 AM"
    },
    "user": "OW",
    "dateSent": "08/15/2025",
    "dateSentOrig": "07/08/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ratke Family Practice",
      "id": "PROV-8049"
    }
  },
  {
    "patient": {
      "name": "Clifton Rau",
      "id": "PAT-57097"
    },
    "serviceDate": "03/21/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$4074.90",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "7:40 PM"
    },
    "user": "EP",
    "dateSent": "06/08/2025",
    "dateSentOrig": "03/24/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "McKenzie Medical Group",
      "id": "PROV-7638"
    }
  },
  {
    "patient": {
      "name": "Alejandro Predovic",
      "id": "PAT-35084"
    },
    "serviceDate": "05/31/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$12163.34",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "5:54 PM"
    },
    "user": "FC",
    "dateSent": "07/07/2025",
    "dateSentOrig": "07/07/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Buckridge Clinic",
      "id": "PROV-4446"
    }
  },
  {
    "patient": {
      "name": "Virginia Hane-Mayer",
      "id": "PAT-14164"
    },
    "serviceDate": "04/08/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$4616.57",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "2:57 PM"
    },
    "user": "TJF",
    "dateSent": "07/06/2025",
    "dateSentOrig": "07/06/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Quitzon Family Practice",
      "id": "PROV-4531"
    }
  },
  {
    "patient": {
      "name": "Della Grady",
      "id": "PAT-20261"
    },
    "serviceDate": "08/27/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$14252.04",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "4:03 PM"
    },
    "user": "ASP",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "West Medical Group",
      "id": "PROV-9164"
    }
  },
  {
    "patient": {
      "name": "Marilyn Conn",
      "id": "PAT-16900"
    },
    "serviceDate": "08/10/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$12213.71",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "12:05 AM"
    },
    "user": "RRD",
    "dateSent": "08/13/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Olson Family Practice",
      "id": "PROV-8777"
    }
  },
  {
    "patient": {
      "name": "Judy Stiedemann Jr.",
      "id": "PAT-75105"
    },
    "serviceDate": "04/25/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$13211.60",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "10:36 PM"
    },
    "user": "LT",
    "dateSent": "09/01/2025",
    "dateSentOrig": "05/23/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Kiehn Associates",
      "id": "PROV-7990"
    }
  },
  {
    "patient": {
      "name": "Nadine Bins",
      "id": "PAT-10748"
    },
    "serviceDate": "03/11/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$6999.15",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "11:38 AM"
    },
    "user": "NNB",
    "dateSent": "04/10/2025",
    "dateSentOrig": "03/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ward Medical Group",
      "id": "PROV-5582"
    }
  },
  {
    "patient": {
      "name": "Kyle Wisoky",
      "id": "PAT-76585"
    },
    "serviceDate": "08/07/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$9638.42",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "6:56 AM"
    },
    "user": "LH",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Ankunding Family Practice",
      "id": "PROV-9180"
    }
  },
  {
    "patient": {
      "name": "Carole Legros I",
      "id": "PAT-75687"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$3880.98",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "12:13 AM"
    },
    "user": "JW",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Nolan Clinic",
      "id": "PROV-9439"
    }
  },
  {
    "patient": {
      "name": "Lana Miller-Stracke",
      "id": "PAT-12410"
    },
    "serviceDate": "08/08/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$3158.70",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "5:05 PM"
    },
    "user": "DK",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Jacobson Associates",
      "id": "PROV-1050"
    }
  },
  {
    "patient": {
      "name": "Richard Hansen III",
      "id": "PAT-64266"
    },
    "serviceDate": "08/10/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$14507.88",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "2:16 AM"
    },
    "user": "DBM",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Jacobson Medical Group",
      "id": "PROV-8576"
    }
  },
  {
    "patient": {
      "name": "Aubrey Von Jr.",
      "id": "PAT-55133"
    },
    "serviceDate": "07/27/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$9179.66",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "10:21 PM"
    },
    "user": "BD",
    "dateSent": "07/28/2025",
    "dateSentOrig": "07/28/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Wolff Medical Group",
      "id": "PROV-5205"
    }
  },
  {
    "patient": {
      "name": "Dr. Harvey Strosin",
      "id": "PAT-14992"
    },
    "serviceDate": "08/10/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$3383.20",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "2:35 AM"
    },
    "user": "VNG",
    "dateSent": "08/15/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Hackett Healthcare",
      "id": "PROV-5136"
    }
  },
  {
    "patient": {
      "name": "Ryan Bruen",
      "id": "PAT-83447"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$11977.78",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "9:50 PM"
    },
    "user": "AA",
    "dateSent": "09/01/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Kassulke Healthcare",
      "id": "PROV-2125"
    }
  },
  {
    "patient": {
      "name": "Clayton Lindgren",
      "id": "PAT-38428"
    },
    "serviceDate": "07/22/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$7783.77",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "10:13 PM"
    },
    "user": "CR",
    "dateSent": "07/25/2025",
    "dateSentOrig": "07/25/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "McKenzie Associates",
      "id": "PROV-1915"
    }
  },
  {
    "patient": {
      "name": "Benny Anderson",
      "id": "PAT-87243"
    },
    "serviceDate": "06/06/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$11292.67",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "3:08 PM"
    },
    "user": "SJ",
    "dateSent": "08/07/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schuppe Family Practice",
      "id": "PROV-3669"
    }
  },
  {
    "patient": {
      "name": "Tara Gerhold-Johnson",
      "id": "PAT-35427"
    },
    "serviceDate": "05/07/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$8402.39",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "1:05 PM"
    },
    "user": "AB",
    "dateSent": "06/19/2025",
    "dateSentOrig": "05/10/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Hills Medical Group",
      "id": "PROV-1596"
    }
  },
  {
    "patient": {
      "name": "Jeremy Wiza",
      "id": "PAT-83706"
    },
    "serviceDate": "08/24/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$11203.41",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "5:11 AM"
    },
    "user": "DMA",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Sanford Associates",
      "id": "PROV-7930"
    }
  },
  {
    "patient": {
      "name": "Darlene Dooley",
      "id": "PAT-37913"
    },
    "serviceDate": "08/26/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$3543.86",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "5:56 AM"
    },
    "user": "RE",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Walsh-O'Conner Healthcare",
      "id": "PROV-2339"
    }
  },
  {
    "patient": {
      "name": "Vivian Howell",
      "id": "PAT-34344"
    },
    "serviceDate": "04/22/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$14187.78",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "12:31 AM"
    },
    "user": "EB",
    "dateSent": "08/16/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Williamson Healthcare",
      "id": "PROV-9494"
    }
  },
  {
    "patient": {
      "name": "Tyler Kertzmann V",
      "id": "PAT-47706"
    },
    "serviceDate": "08/14/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$8310.82",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "9:34 PM"
    },
    "user": "CMK",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Larson Clinic",
      "id": "PROV-9662"
    }
  },
  {
    "patient": {
      "name": "Tracy Tillman",
      "id": "PAT-89218"
    },
    "serviceDate": "03/08/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$14394.45",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "7:06 PM"
    },
    "user": "AT",
    "dateSent": "05/22/2025",
    "dateSentOrig": "05/22/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Casper Associates",
      "id": "PROV-9346"
    }
  },
  {
    "patient": {
      "name": "Mrs. Beulah Stroman",
      "id": "PAT-16346"
    },
    "serviceDate": "03/09/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$13302.92",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "5:37 AM"
    },
    "user": "CU",
    "dateSent": "05/28/2025",
    "dateSentOrig": "05/28/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Heller Family Practice",
      "id": "PROV-9270"
    }
  },
  {
    "patient": {
      "name": "Leah McDermott PhD",
      "id": "PAT-66276"
    },
    "serviceDate": "04/16/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$2865.34",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "6:29 PM"
    },
    "user": "CKW",
    "dateSent": "05/05/2025",
    "dateSentOrig": "04/20/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Greenholt Associates",
      "id": "PROV-3240"
    }
  },
  {
    "patient": {
      "name": "Ray Windler",
      "id": "PAT-85317"
    },
    "serviceDate": "05/07/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$4578.91",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "4:16 PM"
    },
    "user": "KM",
    "dateSent": "06/13/2025",
    "dateSentOrig": "05/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Witting Medical Group",
      "id": "PROV-3654"
    }
  },
  {
    "patient": {
      "name": "Melody O'Kon",
      "id": "PAT-19807"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$5228.31",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "9:43 AM"
    },
    "user": "CB",
    "dateSent": "08/11/2025",
    "dateSentOrig": "08/02/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Cruickshank Healthcare",
      "id": "PROV-3152"
    }
  },
  {
    "patient": {
      "name": "Calvin Zieme I",
      "id": "PAT-72843"
    },
    "serviceDate": "06/10/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$4024.67",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "10:57 AM"
    },
    "user": "IR",
    "dateSent": "07/22/2025",
    "dateSentOrig": "07/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Stamm Medical Group",
      "id": "PROV-2505"
    }
  },
  {
    "patient": {
      "name": "Thomas Wisoky PhD",
      "id": "PAT-41878"
    },
    "serviceDate": "06/02/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$4785.53",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "9:10 PM"
    },
    "user": "TBD",
    "dateSent": "06/18/2025",
    "dateSentOrig": "06/03/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Friesen Clinic",
      "id": "PROV-9202"
    }
  },
  {
    "patient": {
      "name": "Caleb Casper III",
      "id": "PAT-41060"
    },
    "serviceDate": "08/09/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$11034.21",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "1:17 PM"
    },
    "user": "MM",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Pagac Clinic",
      "id": "PROV-1976"
    }
  },
  {
    "patient": {
      "name": "Belinda Kuphal",
      "id": "PAT-17253"
    },
    "serviceDate": "06/07/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$6969.06",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "12:27 AM"
    },
    "user": "MB",
    "dateSent": "06/24/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Swaniawski Clinic",
      "id": "PROV-3435"
    }
  },
  {
    "patient": {
      "name": "Rebecca Rempel",
      "id": "PAT-88279"
    },
    "serviceDate": "07/26/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$3131.85",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "5:49 PM"
    },
    "user": "JK",
    "dateSent": "08/06/2025",
    "dateSentOrig": "07/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Luettgen Medical Group",
      "id": "PROV-9042"
    }
  },
  {
    "patient": {
      "name": "Faye Vandervort",
      "id": "PAT-77273"
    },
    "serviceDate": "03/15/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$10417.86",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "12:03 AM"
    },
    "user": "MW",
    "dateSent": "07/09/2025",
    "dateSentOrig": "07/09/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Borer Clinic",
      "id": "PROV-9125"
    }
  },
  {
    "patient": {
      "name": "Calvin Reichert",
      "id": "PAT-65772"
    },
    "serviceDate": "08/08/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$1142.90",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "5:58 AM"
    },
    "user": "TJM",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Keebler Associates",
      "id": "PROV-8119"
    }
  },
  {
    "patient": {
      "name": "Brandon Reinger",
      "id": "PAT-93324"
    },
    "serviceDate": "05/12/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$10287.52",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "12:40 PM"
    },
    "user": "KM",
    "dateSent": "05/31/2025",
    "dateSentOrig": "05/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schmidt Associates",
      "id": "PROV-2340"
    }
  },
  {
    "patient": {
      "name": "Corey Steuber",
      "id": "PAT-67547"
    },
    "serviceDate": "07/15/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$10708.68",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "1:38 AM"
    },
    "user": "RRR",
    "dateSent": "07/17/2025",
    "dateSentOrig": "07/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kassulke Family Practice",
      "id": "PROV-3059"
    }
  },
  {
    "patient": {
      "name": "Clark Lockman",
      "id": "PAT-47366"
    },
    "serviceDate": "06/13/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$2900.77",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "9:07 PM"
    },
    "user": "LH",
    "dateSent": "06/14/2025",
    "dateSentOrig": "06/14/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "McLaughlin Healthcare",
      "id": "PROV-8869"
    }
  },
  {
    "patient": {
      "name": "Geneva Bernhard",
      "id": "PAT-29544"
    },
    "serviceDate": "08/03/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$724.13",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "12:16 AM"
    },
    "user": "DKM",
    "dateSent": "08/14/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Stroman Medical Group",
      "id": "PROV-8589"
    }
  },
  {
    "patient": {
      "name": "Jimmie Bechtelar III",
      "id": "PAT-96757"
    },
    "serviceDate": "04/28/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$2362.90",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "3:01 AM"
    },
    "user": "EHJ",
    "dateSent": "07/31/2025",
    "dateSentOrig": "07/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Weimann Healthcare",
      "id": "PROV-5847"
    }
  },
  {
    "patient": {
      "name": "Colleen Ward",
      "id": "PAT-95804"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$251.92",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "3:52 PM"
    },
    "user": "DJH",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Daugherty Associates",
      "id": "PROV-2650"
    }
  },
  {
    "patient": {
      "name": "Carla Doyle Sr.",
      "id": "PAT-63441"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$321.50",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "5:49 PM"
    },
    "user": "HPH",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Corwin Medical Group",
      "id": "PROV-8622"
    }
  },
  {
    "patient": {
      "name": "Priscilla Windler",
      "id": "PAT-17362"
    },
    "serviceDate": "07/26/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$11859.73",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "9:54 AM"
    },
    "user": "ENW",
    "dateSent": "08/02/2025",
    "dateSentOrig": "08/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Wyman Associates",
      "id": "PROV-2282"
    }
  },
  {
    "patient": {
      "name": "Cora Goldner",
      "id": "PAT-43820"
    },
    "serviceDate": "08/02/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$6306.36",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "2:52 PM"
    },
    "user": "MAB",
    "dateSent": "08/04/2025",
    "dateSentOrig": "08/03/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Kuhlman Healthcare",
      "id": "PROV-9371"
    }
  },
  {
    "patient": {
      "name": "Clinton Kessler",
      "id": "PAT-24733"
    },
    "serviceDate": "08/20/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$11605.27",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "4:41 AM"
    },
    "user": "JZ",
    "dateSent": "09/04/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Sipes Family Practice",
      "id": "PROV-1156"
    }
  },
  {
    "patient": {
      "name": "Santiago Kilback",
      "id": "PAT-37274"
    },
    "serviceDate": "08/13/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$14721.51",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "11:35 AM"
    },
    "user": "MBV",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Huel Associates",
      "id": "PROV-9068"
    }
  },
  {
    "patient": {
      "name": "Tracy Spinka",
      "id": "PAT-86001"
    },
    "serviceDate": "08/17/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$2437.17",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "12:10 PM"
    },
    "user": "GQB",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Weissnat-Predovic Healthcare",
      "id": "PROV-4949"
    }
  },
  {
    "patient": {
      "name": "Margarita Bogan",
      "id": "PAT-50968"
    },
    "serviceDate": "08/03/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$6444.55",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "10:06 PM"
    },
    "user": "ATK",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Krajcik Family Practice",
      "id": "PROV-6634"
    }
  },
  {
    "patient": {
      "name": "Marianne Heaney-Herman",
      "id": "PAT-26788"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$117.18",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "1:19 AM"
    },
    "user": "MK",
    "dateSent": "08/19/2025",
    "dateSentOrig": "07/09/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Kihn Clinic",
      "id": "PROV-8512"
    }
  },
  {
    "patient": {
      "name": "Dr. Derek Leffler",
      "id": "PAT-76119"
    },
    "serviceDate": "03/22/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$4332.56",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "8:48 PM"
    },
    "user": "MS",
    "dateSent": "04/23/2025",
    "dateSentOrig": "04/23/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Kuphal Associates",
      "id": "PROV-6840"
    }
  },
  {
    "patient": {
      "name": "Javier Quitzon-Kessler",
      "id": "PAT-23192"
    },
    "serviceDate": "08/25/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$3236.26",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "1:29 AM"
    },
    "user": "EFC",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Predovic-Friesen Family Practice",
      "id": "PROV-9817"
    }
  },
  {
    "patient": {
      "name": "Ken Abbott",
      "id": "PAT-25805"
    },
    "serviceDate": "06/20/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$2622.87",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "8:41 PM"
    },
    "user": "MG",
    "dateSent": "07/10/2025",
    "dateSentOrig": "07/10/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Armstrong Family Practice",
      "id": "PROV-5226"
    }
  },
  {
    "patient": {
      "name": "Leslie Kris",
      "id": "PAT-83008"
    },
    "serviceDate": "05/05/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$11419.89",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "7:11 PM"
    },
    "user": "EKD",
    "dateSent": "05/08/2025",
    "dateSentOrig": "05/06/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Leffler Associates",
      "id": "PROV-7881"
    }
  },
  {
    "patient": {
      "name": "Dwight Kunze",
      "id": "PAT-89336"
    },
    "serviceDate": "03/13/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$8928.91",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "1:04 AM"
    },
    "user": "PES",
    "dateSent": "07/25/2025",
    "dateSentOrig": "07/25/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Keeling Associates",
      "id": "PROV-6534"
    }
  },
  {
    "patient": {
      "name": "Myron Kshlerin",
      "id": "PAT-27343"
    },
    "serviceDate": "03/28/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$7683.68",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "7:57 PM"
    },
    "user": "FM",
    "dateSent": "05/26/2025",
    "dateSentOrig": "05/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Considine Medical Group",
      "id": "PROV-9151"
    }
  },
  {
    "patient": {
      "name": "Sharon Feil",
      "id": "PAT-55088"
    },
    "serviceDate": "05/15/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$12616.10",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "2:26 AM"
    },
    "user": "RW",
    "dateSent": "06/08/2025",
    "dateSentOrig": "05/24/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Gislason Healthcare",
      "id": "PROV-2531"
    }
  },
  {
    "patient": {
      "name": "Kari Stroman",
      "id": "PAT-87421"
    },
    "serviceDate": "04/30/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$14410.97",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "10:18 PM"
    },
    "user": "FHV",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Harber Medical Group",
      "id": "PROV-1169"
    }
  },
  {
    "patient": {
      "name": "Bryant Hyatt",
      "id": "PAT-72841"
    },
    "serviceDate": "08/18/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$8697.48",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "3:23 PM"
    },
    "user": "CRW",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Wiegand Associates",
      "id": "PROV-5990"
    }
  },
  {
    "patient": {
      "name": "Ms. Eloise Mertz",
      "id": "PAT-96539"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$11998.11",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "9:29 AM"
    },
    "user": "KEG",
    "dateSent": "06/22/2025",
    "dateSentOrig": "06/14/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Grady Medical Group",
      "id": "PROV-2872"
    }
  },
  {
    "patient": {
      "name": "Lindsey McClure III",
      "id": "PAT-36846"
    },
    "serviceDate": "07/01/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$883.10",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "2:26 AM"
    },
    "user": "BS",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/19/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Jakubowski Medical Group",
      "id": "PROV-3560"
    }
  },
  {
    "patient": {
      "name": "Morris Wisozk-Walker",
      "id": "PAT-46613"
    },
    "serviceDate": "03/29/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$14987.41",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "1:44 AM"
    },
    "user": "ERH",
    "dateSent": "07/29/2025",
    "dateSentOrig": "07/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Howell Associates",
      "id": "PROV-4120"
    }
  },
  {
    "patient": {
      "name": "Kara Goldner",
      "id": "PAT-74433"
    },
    "serviceDate": "06/29/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$8245.56",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "2:44 AM"
    },
    "user": "LM",
    "dateSent": "07/22/2025",
    "dateSentOrig": "07/07/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Marvin Healthcare",
      "id": "PROV-4144"
    }
  },
  {
    "patient": {
      "name": "Lucia Bode",
      "id": "PAT-65350"
    },
    "serviceDate": "04/07/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$2328.99",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "12:48 AM"
    },
    "user": "DAR",
    "dateSent": "06/21/2025",
    "dateSentOrig": "05/24/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Stracke Clinic",
      "id": "PROV-5808"
    }
  },
  {
    "patient": {
      "name": "Richard Vandervort",
      "id": "PAT-51037"
    },
    "serviceDate": "05/04/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$10318.28",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "10:21 AM"
    },
    "user": "KRL",
    "dateSent": "06/11/2025",
    "dateSentOrig": "05/21/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Orn Clinic",
      "id": "PROV-9915"
    }
  },
  {
    "patient": {
      "name": "Dean Carroll-Leuschke",
      "id": "PAT-79960"
    },
    "serviceDate": "05/25/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$12890.83",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "5:55 AM"
    },
    "user": "QZ",
    "dateSent": "06/22/2025",
    "dateSentOrig": "05/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Cremin Healthcare",
      "id": "PROV-1547"
    }
  },
  {
    "patient": {
      "name": "Tracey Nolan",
      "id": "PAT-21690"
    },
    "serviceDate": "06/07/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$314.23",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "2:43 AM"
    },
    "user": "LRL",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Lemke Medical Group",
      "id": "PROV-5357"
    }
  },
  {
    "patient": {
      "name": "Sherri Dach",
      "id": "PAT-83859"
    },
    "serviceDate": "08/09/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$1384.56",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "12:37 PM"
    },
    "user": "SH",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/19/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "MacGyver Associates",
      "id": "PROV-1582"
    }
  },
  {
    "patient": {
      "name": "Jeffery Monahan",
      "id": "PAT-61383"
    },
    "serviceDate": "05/25/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$13485.73",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "2:04 PM"
    },
    "user": "MDH",
    "dateSent": "07/14/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Halvorson Healthcare",
      "id": "PROV-7884"
    }
  },
  {
    "patient": {
      "name": "Alton Corwin",
      "id": "PAT-22667"
    },
    "serviceDate": "06/24/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$5446.01",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "7:14 AM"
    },
    "user": "CPF",
    "dateSent": "07/16/2025",
    "dateSentOrig": "07/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kris Associates",
      "id": "PROV-4796"
    }
  },
  {
    "patient": {
      "name": "Abraham Schaefer MD",
      "id": "PAT-89422"
    },
    "serviceDate": "05/18/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$8290.70",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "4:39 PM"
    },
    "user": "TG",
    "dateSent": "06/27/2025",
    "dateSentOrig": "06/27/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Morissette Associates",
      "id": "PROV-9699"
    }
  },
  {
    "patient": {
      "name": "Terrance Lindgren DVM",
      "id": "PAT-13825"
    },
    "serviceDate": "08/21/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$6452.69",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "5:56 AM"
    },
    "user": "GS",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Brown Medical Group",
      "id": "PROV-8609"
    }
  },
  {
    "patient": {
      "name": "Wilma Smitham",
      "id": "PAT-58467"
    },
    "serviceDate": "08/13/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$12684.65",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "2:19 AM"
    },
    "user": "MKW",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Schimmel Associates",
      "id": "PROV-4329"
    }
  },
  {
    "patient": {
      "name": "Jonathan Waters",
      "id": "PAT-21015"
    },
    "serviceDate": "08/07/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$8740.57",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "1:25 AM"
    },
    "user": "SD",
    "dateSent": "09/01/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Jenkins Medical Group",
      "id": "PROV-8865"
    }
  },
  {
    "patient": {
      "name": "Dr. Julio Hansen",
      "id": "PAT-84251"
    },
    "serviceDate": "08/23/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$11754.45",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "4:29 AM"
    },
    "user": "HSM",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Okuneva Healthcare",
      "id": "PROV-6462"
    }
  },
  {
    "patient": {
      "name": "Dr. Saul Schmidt",
      "id": "PAT-89619"
    },
    "serviceDate": "04/13/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$486.12",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "2:12 AM"
    },
    "user": "SRC",
    "dateSent": "07/04/2025",
    "dateSentOrig": "05/21/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Okuneva-Jakubowski Healthcare",
      "id": "PROV-2718"
    }
  },
  {
    "patient": {
      "name": "Omar Heller",
      "id": "PAT-28205"
    },
    "serviceDate": "08/19/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$6301.83",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "9:29 AM"
    },
    "user": "KBM",
    "dateSent": "09/02/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Kub Clinic",
      "id": "PROV-3968"
    }
  },
  {
    "patient": {
      "name": "Dr. Conrad Sipes",
      "id": "PAT-69476"
    },
    "serviceDate": "04/04/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$3504.10",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "6:22 PM"
    },
    "user": "ESM",
    "dateSent": "05/29/2025",
    "dateSentOrig": "04/06/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Frami Associates",
      "id": "PROV-2933"
    }
  },
  {
    "patient": {
      "name": "Lorena Will",
      "id": "PAT-26318"
    },
    "serviceDate": "05/08/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$13070.41",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "1:27 PM"
    },
    "user": "DG",
    "dateSent": "06/12/2025",
    "dateSentOrig": "06/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Kuhn Family Practice",
      "id": "PROV-3986"
    }
  },
  {
    "patient": {
      "name": "Mrs. Candice Dare",
      "id": "PAT-48479"
    },
    "serviceDate": "06/30/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$1315.41",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "11:16 AM"
    },
    "user": "NEH",
    "dateSent": "09/03/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Denesik Healthcare",
      "id": "PROV-9107"
    }
  },
  {
    "patient": {
      "name": "Caroline Marvin",
      "id": "PAT-25811"
    },
    "serviceDate": "06/21/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$3741.52",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "4:24 AM"
    },
    "user": "BFW",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Smitham Associates",
      "id": "PROV-3972"
    }
  },
  {
    "patient": {
      "name": "Benny Hahn",
      "id": "PAT-96680"
    },
    "serviceDate": "03/21/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$6130.69",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "8:30 PM"
    },
    "user": "EJW",
    "dateSent": "06/04/2025",
    "dateSentOrig": "05/20/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Howell Clinic",
      "id": "PROV-8259"
    }
  },
  {
    "patient": {
      "name": "Rudolph Bogisich",
      "id": "PAT-14888"
    },
    "serviceDate": "04/02/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$11378.12",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "3:47 PM"
    },
    "user": "JD",
    "dateSent": "07/18/2025",
    "dateSentOrig": "06/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Thiel Healthcare",
      "id": "PROV-9351"
    }
  },
  {
    "patient": {
      "name": "Homer Mayer I",
      "id": "PAT-45859"
    },
    "serviceDate": "06/23/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$3068.59",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "7:12 AM"
    },
    "user": "EE",
    "dateSent": "06/25/2025",
    "dateSentOrig": "06/25/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Renner Associates",
      "id": "PROV-3926"
    }
  },
  {
    "patient": {
      "name": "Celia Heller",
      "id": "PAT-73819"
    },
    "serviceDate": "04/21/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$9508.85",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "8:37 PM"
    },
    "user": "KNT",
    "dateSent": "07/02/2025",
    "dateSentOrig": "06/27/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Huels Family Practice",
      "id": "PROV-8759"
    }
  },
  {
    "patient": {
      "name": "Naomi Jenkins",
      "id": "PAT-70587"
    },
    "serviceDate": "06/20/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$11244.80",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "4:00 AM"
    },
    "user": "MW",
    "dateSent": "08/21/2025",
    "dateSentOrig": "06/29/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Wiegand Family Practice",
      "id": "PROV-1757"
    }
  },
  {
    "patient": {
      "name": "Georgia Fahey",
      "id": "PAT-76267"
    },
    "serviceDate": "05/06/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$552.03",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "4:30 PM"
    },
    "user": "JKF",
    "dateSent": "08/19/2025",
    "dateSentOrig": "06/18/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Abernathy-Orn Clinic",
      "id": "PROV-1526"
    }
  },
  {
    "patient": {
      "name": "Wendy Wilkinson",
      "id": "PAT-14498"
    },
    "serviceDate": "07/17/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$4216.86",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "1:27 PM"
    },
    "user": "AK",
    "dateSent": "08/03/2025",
    "dateSentOrig": "08/03/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Stamm-Simonis Family Practice",
      "id": "PROV-2050"
    }
  },
  {
    "patient": {
      "name": "Brandy Bartoletti I",
      "id": "PAT-60757"
    },
    "serviceDate": "08/11/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$5560.74",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "7:01 AM"
    },
    "user": "BH",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Goodwin Family Practice",
      "id": "PROV-3166"
    }
  },
  {
    "patient": {
      "name": "Earl Schuppe",
      "id": "PAT-53738"
    },
    "serviceDate": "05/26/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$7885.29",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "12:33 PM"
    },
    "user": "RF",
    "dateSent": "06/04/2025",
    "dateSentOrig": "06/04/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Armstrong Healthcare",
      "id": "PROV-8478"
    }
  },
  {
    "patient": {
      "name": "Eula Murphy",
      "id": "PAT-43239"
    },
    "serviceDate": "08/29/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$7947.81",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "4:02 AM"
    },
    "user": "EH",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Schmitt Clinic",
      "id": "PROV-8548"
    }
  },
  {
    "patient": {
      "name": "Steven Leffler-Lang",
      "id": "PAT-69337"
    },
    "serviceDate": "05/20/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$13160.14",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "5:26 PM"
    },
    "user": "MW",
    "dateSent": "05/30/2025",
    "dateSentOrig": "05/21/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Jenkins-Weber Family Practice",
      "id": "PROV-7799"
    }
  },
  {
    "patient": {
      "name": "Noel Ledner",
      "id": "PAT-22195"
    },
    "serviceDate": "07/17/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$11966.83",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "8:51 AM"
    },
    "user": "LEP",
    "dateSent": "08/22/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Corwin Associates",
      "id": "PROV-8789"
    }
  },
  {
    "patient": {
      "name": "Dr. Pauline Prosacco",
      "id": "PAT-19498"
    },
    "serviceDate": "06/29/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$642.75",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "5:45 PM"
    },
    "user": "FG",
    "dateSent": "08/09/2025",
    "dateSentOrig": "07/01/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Hamill Medical Group",
      "id": "PROV-1333"
    }
  },
  {
    "patient": {
      "name": "Lucia Homenick",
      "id": "PAT-64607"
    },
    "serviceDate": "06/23/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$5535.46",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "1:36 AM"
    },
    "user": "REW",
    "dateSent": "09/01/2025",
    "dateSentOrig": "07/25/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Borer Associates",
      "id": "PROV-6467"
    }
  },
  {
    "patient": {
      "name": "Pamela Borer",
      "id": "PAT-37176"
    },
    "serviceDate": "08/13/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$7019.98",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "9:14 PM"
    },
    "user": "GD",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Nader Medical Group",
      "id": "PROV-5555"
    }
  },
  {
    "patient": {
      "name": "Mr. Leslie Waelchi",
      "id": "PAT-70168"
    },
    "serviceDate": "03/26/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$6937.27",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "12:49 PM"
    },
    "user": "LM",
    "dateSent": "08/14/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "McCullough Clinic",
      "id": "PROV-7193"
    }
  },
  {
    "patient": {
      "name": "Josephine Purdy",
      "id": "PAT-76088"
    },
    "serviceDate": "05/28/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$13262.53",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "4:03 PM"
    },
    "user": "CAM",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Ruecker Healthcare",
      "id": "PROV-8997"
    }
  },
  {
    "patient": {
      "name": "Glenda Schmeler",
      "id": "PAT-65696"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$7611.67",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "1:56 AM"
    },
    "user": "BH",
    "dateSent": "06/25/2025",
    "dateSentOrig": "06/14/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Cole Clinic",
      "id": "PROV-4111"
    }
  },
  {
    "patient": {
      "name": "Toby Hane Jr.",
      "id": "PAT-32667"
    },
    "serviceDate": "04/27/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$6081.26",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "6:24 PM"
    },
    "user": "EAR",
    "dateSent": "08/12/2025",
    "dateSentOrig": "05/15/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Ratke Healthcare",
      "id": "PROV-8212"
    }
  },
  {
    "patient": {
      "name": "Roger Schaefer",
      "id": "PAT-68034"
    },
    "serviceDate": "04/12/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$4948.14",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "10:50 PM"
    },
    "user": "OM",
    "dateSent": "04/12/2025",
    "dateSentOrig": "04/12/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Von Clinic",
      "id": "PROV-7841"
    }
  },
  {
    "patient": {
      "name": "Marianne Wolf",
      "id": "PAT-27779"
    },
    "serviceDate": "07/26/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$6165.67",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "12:20 PM"
    },
    "user": "NB",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Bogan-Roberts Medical Group",
      "id": "PROV-3360"
    }
  },
  {
    "patient": {
      "name": "Priscilla Rice-McLaughlin",
      "id": "PAT-64760"
    },
    "serviceDate": "08/26/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$7715.02",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "5:35 AM"
    },
    "user": "NR",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Schaden Medical Group",
      "id": "PROV-8588"
    }
  },
  {
    "patient": {
      "name": "Marcos Reinger",
      "id": "PAT-97056"
    },
    "serviceDate": "04/07/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$11024.03",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "10:40 PM"
    },
    "user": "EB",
    "dateSent": "08/27/2025",
    "dateSentOrig": "07/01/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Jakubowski Medical Group",
      "id": "PROV-7365"
    }
  },
  {
    "patient": {
      "name": "Dr. Randal Fahey",
      "id": "PAT-85550"
    },
    "serviceDate": "07/21/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$14991.10",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "7:50 PM"
    },
    "user": "SDH",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Pfannerstill Medical Group",
      "id": "PROV-6725"
    }
  },
  {
    "patient": {
      "name": "Mr. Orville Kertzmann",
      "id": "PAT-61737"
    },
    "serviceDate": "03/28/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$4185.13",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "7:08 AM"
    },
    "user": "KAF",
    "dateSent": "06/02/2025",
    "dateSentOrig": "06/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Huel Associates",
      "id": "PROV-7099"
    }
  },
  {
    "patient": {
      "name": "Ron Purdy Sr.",
      "id": "PAT-37521"
    },
    "serviceDate": "05/16/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$9410.25",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "8:26 PM"
    },
    "user": "HSR",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Mann Associates",
      "id": "PROV-8386"
    }
  },
  {
    "patient": {
      "name": "Gregg Padberg Sr.",
      "id": "PAT-44207"
    },
    "serviceDate": "03/29/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$9173.47",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "3:18 AM"
    },
    "user": "HRB",
    "dateSent": "04/24/2025",
    "dateSentOrig": "04/05/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Howell Family Practice",
      "id": "PROV-1892"
    }
  },
  {
    "patient": {
      "name": "Sabrina Zboncak",
      "id": "PAT-37785"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$8208.50",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "11:19 AM"
    },
    "user": "CNR",
    "dateSent": "06/17/2025",
    "dateSentOrig": "06/11/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Ebert-Hilpert Associates",
      "id": "PROV-7362"
    }
  },
  {
    "patient": {
      "name": "Tonya Franecki",
      "id": "PAT-43708"
    },
    "serviceDate": "07/29/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$12379.30",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "8:35 PM"
    },
    "user": "KK",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Thompson Medical Group",
      "id": "PROV-8438"
    }
  },
  {
    "patient": {
      "name": "James O'Kon",
      "id": "PAT-63754"
    },
    "serviceDate": "08/24/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$11884.91",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "1:09 AM"
    },
    "user": "EL",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Medhurst Family Practice",
      "id": "PROV-8450"
    }
  },
  {
    "patient": {
      "name": "Miss Mandy Kris",
      "id": "PAT-37735"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$9612.16",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/04/2025",
      "time": "7:14 PM"
    },
    "user": "LAT",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hickle Family Practice",
      "id": "PROV-2771"
    }
  },
  {
    "patient": {
      "name": "Fannie Wolff",
      "id": "PAT-40602"
    },
    "serviceDate": "04/23/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$436.97",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "12:35 PM"
    },
    "user": "EC",
    "dateSent": "07/01/2025",
    "dateSentOrig": "07/01/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Turner Associates",
      "id": "PROV-5432"
    }
  },
  {
    "patient": {
      "name": "Marlene Hegmann",
      "id": "PAT-88627"
    },
    "serviceDate": "08/25/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$8367.57",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "1:30 AM"
    },
    "user": "FRM",
    "dateSent": "09/03/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Lehner Family Practice",
      "id": "PROV-6858"
    }
  },
  {
    "patient": {
      "name": "Martin Koepp",
      "id": "PAT-28583"
    },
    "serviceDate": "07/09/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$13630.84",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "4:01 PM"
    },
    "user": "FAS",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Monahan Healthcare",
      "id": "PROV-8659"
    }
  },
  {
    "patient": {
      "name": "Jesus Lind",
      "id": "PAT-17970"
    },
    "serviceDate": "03/31/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$6653.59",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "5:09 AM"
    },
    "user": "MJM",
    "dateSent": "07/12/2025",
    "dateSentOrig": "06/19/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Miller Healthcare",
      "id": "PROV-6712"
    }
  },
  {
    "patient": {
      "name": "Evan Sawayn",
      "id": "PAT-57246"
    },
    "serviceDate": "06/08/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$12969.05",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "4:56 PM"
    },
    "user": "ML",
    "dateSent": "08/22/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Stark-Davis Healthcare",
      "id": "PROV-2668"
    }
  },
  {
    "patient": {
      "name": "Fredrick Hodkiewicz",
      "id": "PAT-12218"
    },
    "serviceDate": "04/13/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$4586.72",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "4:19 PM"
    },
    "user": "MGH",
    "dateSent": "06/19/2025",
    "dateSentOrig": "05/18/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Kutch Healthcare",
      "id": "PROV-6404"
    }
  },
  {
    "patient": {
      "name": "Allen Jacobson",
      "id": "PAT-55408"
    },
    "serviceDate": "05/01/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$5133.86",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "7:33 AM"
    },
    "user": "ZKS",
    "dateSent": "05/21/2025",
    "dateSentOrig": "05/20/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Cronin Medical Group",
      "id": "PROV-2344"
    }
  },
  {
    "patient": {
      "name": "Courtney Hamill",
      "id": "PAT-39546"
    },
    "serviceDate": "08/18/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$3818.29",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "9:27 PM"
    },
    "user": "AB",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ondricka Associates",
      "id": "PROV-4868"
    }
  },
  {
    "patient": {
      "name": "Maureen Koelpin",
      "id": "PAT-56319"
    },
    "serviceDate": "06/04/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$13286.24",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "6:02 AM"
    },
    "user": "RBS",
    "dateSent": "06/11/2025",
    "dateSentOrig": "06/11/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Hand Healthcare",
      "id": "PROV-1477"
    }
  },
  {
    "patient": {
      "name": "Mr. Mathew Cummings",
      "id": "PAT-19001"
    },
    "serviceDate": "06/24/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$8002.32",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "9:00 PM"
    },
    "user": "AM",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Lesch Healthcare",
      "id": "PROV-4751"
    }
  },
  {
    "patient": {
      "name": "Meredith Hackett",
      "id": "PAT-30625"
    },
    "serviceDate": "07/12/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$12822.34",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "9:14 PM"
    },
    "user": "JD",
    "dateSent": "07/15/2025",
    "dateSentOrig": "07/15/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Lebsack Family Practice",
      "id": "PROV-7460"
    }
  }
];

export default insuranceClaimsData;

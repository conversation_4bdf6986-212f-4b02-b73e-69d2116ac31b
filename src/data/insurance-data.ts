// Auto-generated insurance claims data
// Generated on: 2025-09-02T15:09:21.171Z
// Total records: 300

import { ClaimRowData } from '../components/ClaimRow';

export const insuranceClaimsData: ClaimRowData[] = [
  {
    "patient": {
      "name": "<PERSON><PERSON><PERSON>",
      "id": "PAT-98563"
    },
    "serviceDate": "03/23/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$134.35",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "1:44 PM"
    },
    "user": "MB",
    "dateSent": "03/24/2025",
    "dateSentOrig": "03/24/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hyatt Family Practice",
      "id": "PROV-5251"
    }
  },
  {
    "patient": {
      "name": "<PERSON> <PERSON><PERSON>",
      "id": "PAT-94606"
    },
    "serviceDate": "07/15/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$12844.98",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "10:26 PM"
    },
    "user": "JAK",
    "dateSent": "07/22/2025",
    "dateSentOrig": "07/22/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Krajcik Family Practice",
      "id": "PROV-6723"
    }
  },
  {
    "patient": {
      "name": "Alfonso Haley",
      "id": "PAT-39802"
    },
    "serviceDate": "05/20/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$7170.13",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "7:28 AM"
    },
    "user": "JPS",
    "dateSent": "08/24/2025",
    "dateSentOrig": "06/29/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Hansen Clinic",
      "id": "PROV-5766"
    }
  },
  {
    "patient": {
      "name": "Johanna Mayert",
      "id": "PAT-76676"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$8949.62",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "11:07 AM"
    },
    "user": "CPP",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Langosh Family Practice",
      "id": "PROV-2684"
    }
  },
  {
    "patient": {
      "name": "Phil Jacobi",
      "id": "PAT-10347"
    },
    "serviceDate": "05/13/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$2720.81",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "11:49 PM"
    },
    "user": "MAD",
    "dateSent": "07/06/2025",
    "dateSentOrig": "07/06/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Harris Associates",
      "id": "PROV-2519"
    }
  },
  {
    "patient": {
      "name": "Samuel Hammes DVM",
      "id": "PAT-70274"
    },
    "serviceDate": "08/11/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$7942.50",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "8:31 AM"
    },
    "user": "DB",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Beer Medical Group",
      "id": "PROV-9222"
    }
  },
  {
    "patient": {
      "name": "Ms. Misty Larson",
      "id": "PAT-24931"
    },
    "serviceDate": "04/15/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$3669.02",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "5:23 PM"
    },
    "user": "YBA",
    "dateSent": "08/01/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Volkman Clinic",
      "id": "PROV-4798"
    }
  },
  {
    "patient": {
      "name": "Sue Pollich",
      "id": "PAT-43020"
    },
    "serviceDate": "07/02/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$8624.89",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "6:07 PM"
    },
    "user": "MKR",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Senger-Metz Clinic",
      "id": "PROV-8415"
    }
  },
  {
    "patient": {
      "name": "Owen Thiel",
      "id": "PAT-41250"
    },
    "serviceDate": "04/19/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$13000.69",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "9:56 AM"
    },
    "user": "DBS",
    "dateSent": "04/27/2025",
    "dateSentOrig": "04/27/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Botsford Medical Group",
      "id": "PROV-4120"
    }
  },
  {
    "patient": {
      "name": "Christina Grady",
      "id": "PAT-96448"
    },
    "serviceDate": "04/08/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$10367.03",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "8:10 AM"
    },
    "user": "MBL",
    "dateSent": "05/17/2025",
    "dateSentOrig": "04/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Halvorson-Collins Associates",
      "id": "PROV-3330"
    }
  },
  {
    "patient": {
      "name": "Tami Erdman",
      "id": "PAT-13800"
    },
    "serviceDate": "03/18/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$8429.12",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "2:13 PM"
    },
    "user": "CB",
    "dateSent": "06/06/2025",
    "dateSentOrig": "05/14/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Reynolds Medical Group",
      "id": "PROV-7554"
    }
  },
  {
    "patient": {
      "name": "Beulah Krajcik",
      "id": "PAT-38421"
    },
    "serviceDate": "03/21/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$11026.26",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "1:35 AM"
    },
    "user": "AF",
    "dateSent": "07/17/2025",
    "dateSentOrig": "07/17/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Harber Healthcare",
      "id": "PROV-5115"
    }
  },
  {
    "patient": {
      "name": "Kenny Rodriguez",
      "id": "PAT-21769"
    },
    "serviceDate": "03/21/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$14544.20",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "12:04 AM"
    },
    "user": "CGW",
    "dateSent": "05/04/2025",
    "dateSentOrig": "04/28/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kling Family Practice",
      "id": "PROV-9064"
    }
  },
  {
    "patient": {
      "name": "Van Okuneva",
      "id": "PAT-13735"
    },
    "serviceDate": "05/19/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$11387.92",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "8:27 PM"
    },
    "user": "GB",
    "dateSent": "06/04/2025",
    "dateSentOrig": "05/30/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Feeney Medical Group",
      "id": "PROV-3733"
    }
  },
  {
    "patient": {
      "name": "Derrick Terry",
      "id": "PAT-13197"
    },
    "serviceDate": "03/14/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$5316.32",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "8:04 AM"
    },
    "user": "CB",
    "dateSent": "06/04/2025",
    "dateSentOrig": "06/04/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Shields Family Practice",
      "id": "PROV-7980"
    }
  },
  {
    "patient": {
      "name": "Charlene Abshire",
      "id": "PAT-45908"
    },
    "serviceDate": "04/19/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$3253.12",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "4:26 AM"
    },
    "user": "YGR",
    "dateSent": "05/13/2025",
    "dateSentOrig": "04/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Cartwright Healthcare",
      "id": "PROV-9897"
    }
  },
  {
    "patient": {
      "name": "Tammy Cole",
      "id": "PAT-48985"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$4670.41",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "6:53 AM"
    },
    "user": "MB",
    "dateSent": "04/23/2025",
    "dateSentOrig": "04/23/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Considine Associates",
      "id": "PROV-8077"
    }
  },
  {
    "patient": {
      "name": "Jessie Dooley",
      "id": "PAT-94878"
    },
    "serviceDate": "07/22/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$11761.87",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "6:23 PM"
    },
    "user": "LS",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Howe Associates",
      "id": "PROV-8417"
    }
  },
  {
    "patient": {
      "name": "Rebecca Zemlak",
      "id": "PAT-92739"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$8360.78",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "2:32 AM"
    },
    "user": "JF",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Rippin Healthcare",
      "id": "PROV-9447"
    }
  },
  {
    "patient": {
      "name": "Jeremiah Fay",
      "id": "PAT-91513"
    },
    "serviceDate": "03/14/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$4244.10",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "7:17 AM"
    },
    "user": "SH",
    "dateSent": "06/24/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Cole Family Practice",
      "id": "PROV-5542"
    }
  },
  {
    "patient": {
      "name": "Clifford Metz",
      "id": "PAT-65714"
    },
    "serviceDate": "06/07/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$3494.64",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "8:22 PM"
    },
    "user": "JW",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Gleason Clinic",
      "id": "PROV-3330"
    }
  },
  {
    "patient": {
      "name": "Frederick Lakin",
      "id": "PAT-99713"
    },
    "serviceDate": "06/11/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$10811.47",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "9:07 AM"
    },
    "user": "IG",
    "dateSent": "07/12/2025",
    "dateSentOrig": "07/03/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Wuckert Associates",
      "id": "PROV-3924"
    }
  },
  {
    "patient": {
      "name": "Henry Jacobi",
      "id": "PAT-67256"
    },
    "serviceDate": "06/26/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$9299.42",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "11:21 PM"
    },
    "user": "AH",
    "dateSent": "07/26/2025",
    "dateSentOrig": "07/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Terry Medical Group",
      "id": "PROV-7865"
    }
  },
  {
    "patient": {
      "name": "Jon Feeney",
      "id": "PAT-30058"
    },
    "serviceDate": "04/18/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$4725.27",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "3:47 AM"
    },
    "user": "PHP",
    "dateSent": "05/04/2025",
    "dateSentOrig": "04/26/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Feil Medical Group",
      "id": "PROV-1162"
    }
  },
  {
    "patient": {
      "name": "Patsy Morar",
      "id": "PAT-53688"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$511.05",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "5:36 PM"
    },
    "user": "LN",
    "dateSent": "07/08/2025",
    "dateSentOrig": "06/23/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kuphal Associates",
      "id": "PROV-8097"
    }
  },
  {
    "patient": {
      "name": "Kellie Gulgowski",
      "id": "PAT-72078"
    },
    "serviceDate": "05/20/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$5625.71",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "2:54 AM"
    },
    "user": "DAW",
    "dateSent": "07/03/2025",
    "dateSentOrig": "07/03/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "McDermott Clinic",
      "id": "PROV-8169"
    }
  },
  {
    "patient": {
      "name": "Tracy Gorczany",
      "id": "PAT-43132"
    },
    "serviceDate": "07/21/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$11140.10",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "9:33 AM"
    },
    "user": "DO",
    "dateSent": "08/22/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Franecki Clinic",
      "id": "PROV-7119"
    }
  },
  {
    "patient": {
      "name": "Ginger Quitzon",
      "id": "PAT-92079"
    },
    "serviceDate": "06/17/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$5213.48",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "7:30 PM"
    },
    "user": "WG",
    "dateSent": "07/12/2025",
    "dateSentOrig": "07/12/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Ratke Clinic",
      "id": "PROV-4435"
    }
  },
  {
    "patient": {
      "name": "Raymond Morar",
      "id": "PAT-26720"
    },
    "serviceDate": "06/28/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$1576.48",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "9:27 PM"
    },
    "user": "BRW",
    "dateSent": "06/29/2025",
    "dateSentOrig": "06/28/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Wunsch Family Practice",
      "id": "PROV-1326"
    }
  },
  {
    "patient": {
      "name": "Merle Goyette-Schinner",
      "id": "PAT-93993"
    },
    "serviceDate": "08/04/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$2290.16",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "11:39 AM"
    },
    "user": "TS",
    "dateSent": "08/05/2025",
    "dateSentOrig": "08/05/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Crist Clinic",
      "id": "PROV-7315"
    }
  },
  {
    "patient": {
      "name": "Dr. Louise Barton",
      "id": "PAT-72741"
    },
    "serviceDate": "04/16/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$3953.27",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "8:14 AM"
    },
    "user": "MB",
    "dateSent": "06/08/2025",
    "dateSentOrig": "05/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ratke Family Practice",
      "id": "PROV-5863"
    }
  },
  {
    "patient": {
      "name": "Kay Koelpin",
      "id": "PAT-45009"
    },
    "serviceDate": "08/17/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$10409.67",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "7:56 AM"
    },
    "user": "MRL",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Heaney Healthcare",
      "id": "PROV-5232"
    }
  },
  {
    "patient": {
      "name": "Rachel Brakus-Lockman",
      "id": "PAT-10344"
    },
    "serviceDate": "06/21/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$8426.72",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "11:23 PM"
    },
    "user": "EAH",
    "dateSent": "07/19/2025",
    "dateSentOrig": "07/11/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Walsh Associates",
      "id": "PROV-2202"
    }
  },
  {
    "patient": {
      "name": "Cory Toy-Kerluke",
      "id": "PAT-89547"
    },
    "serviceDate": "03/13/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$2853.30",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "11:21 AM"
    },
    "user": "QTS",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Brekke Clinic",
      "id": "PROV-8393"
    }
  },
  {
    "patient": {
      "name": "Bradley Volkman",
      "id": "PAT-44564"
    },
    "serviceDate": "07/27/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$3996.02",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "8:40 PM"
    },
    "user": "MS",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Jast Medical Group",
      "id": "PROV-4210"
    }
  },
  {
    "patient": {
      "name": "Gwendolyn Metz",
      "id": "PAT-54159"
    },
    "serviceDate": "03/26/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$2513.31",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "12:57 PM"
    },
    "user": "JTB",
    "dateSent": "08/07/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Leuschke Associates",
      "id": "PROV-6546"
    }
  },
  {
    "patient": {
      "name": "Tonya Hayes",
      "id": "PAT-26081"
    },
    "serviceDate": "07/08/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$12364.67",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "1:36 AM"
    },
    "user": "DEM",
    "dateSent": "07/12/2025",
    "dateSentOrig": "07/10/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Maggio Family Practice",
      "id": "PROV-5446"
    }
  },
  {
    "patient": {
      "name": "Guillermo Osinski Sr.",
      "id": "PAT-82035"
    },
    "serviceDate": "06/24/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$7765.66",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "7:59 AM"
    },
    "user": "IJR",
    "dateSent": "08/11/2025",
    "dateSentOrig": "07/01/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Smitham Family Practice",
      "id": "PROV-9690"
    }
  },
  {
    "patient": {
      "name": "Carolyn Kihn",
      "id": "PAT-55948"
    },
    "serviceDate": "03/30/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$14075.74",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "2:01 AM"
    },
    "user": "AB",
    "dateSent": "03/31/2025",
    "dateSentOrig": "03/31/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Nienow Associates",
      "id": "PROV-8185"
    }
  },
  {
    "patient": {
      "name": "Dr. Dave Nicolas",
      "id": "PAT-27840"
    },
    "serviceDate": "03/07/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$814.09",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "12:48 AM"
    },
    "user": "ASP",
    "dateSent": "07/13/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Nikolaus Medical Group",
      "id": "PROV-4685"
    }
  },
  {
    "patient": {
      "name": "Jamie Hermiston",
      "id": "PAT-57914"
    },
    "serviceDate": "06/10/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$11703.15",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "6:13 AM"
    },
    "user": "JH",
    "dateSent": "09/01/2025",
    "dateSentOrig": "09/01/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ledner Associates",
      "id": "PROV-2257"
    }
  },
  {
    "patient": {
      "name": "Jane Franey",
      "id": "PAT-92728"
    },
    "serviceDate": "06/09/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$11706.26",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "12:28 AM"
    },
    "user": "KNS",
    "dateSent": "06/17/2025",
    "dateSentOrig": "06/12/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Reichert Healthcare",
      "id": "PROV-5511"
    }
  },
  {
    "patient": {
      "name": "Benjamin Carroll",
      "id": "PAT-47089"
    },
    "serviceDate": "03/26/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$785.69",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "12:42 AM"
    },
    "user": "DH",
    "dateSent": "04/03/2025",
    "dateSentOrig": "03/27/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Paucek Medical Group",
      "id": "PROV-3096"
    }
  },
  {
    "patient": {
      "name": "Brittany Reynolds",
      "id": "PAT-66184"
    },
    "serviceDate": "07/15/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$3794.38",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "7:02 AM"
    },
    "user": "GS",
    "dateSent": "08/06/2025",
    "dateSentOrig": "07/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Thompson Family Practice",
      "id": "PROV-1014"
    }
  },
  {
    "patient": {
      "name": "Kenny Bernhard Jr.",
      "id": "PAT-35111"
    },
    "serviceDate": "05/06/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$13243.83",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "4:02 AM"
    },
    "user": "DCB",
    "dateSent": "07/13/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Rodriguez Healthcare",
      "id": "PROV-8332"
    }
  },
  {
    "patient": {
      "name": "Tiffany Hamill",
      "id": "PAT-10425"
    },
    "serviceDate": "04/01/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$6097.43",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "8:10 AM"
    },
    "user": "LT",
    "dateSent": "08/04/2025",
    "dateSentOrig": "08/04/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Lang Clinic",
      "id": "PROV-4884"
    }
  },
  {
    "patient": {
      "name": "Timmy Bode MD",
      "id": "PAT-76987"
    },
    "serviceDate": "05/12/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$14698.55",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "3:25 PM"
    },
    "user": "BPS",
    "dateSent": "08/13/2025",
    "dateSentOrig": "05/14/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "McDermott Healthcare",
      "id": "PROV-8109"
    }
  },
  {
    "patient": {
      "name": "Amy Walker",
      "id": "PAT-15168"
    },
    "serviceDate": "07/06/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$4463.08",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "1:42 PM"
    },
    "user": "HR",
    "dateSent": "08/03/2025",
    "dateSentOrig": "07/11/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Predovic Associates",
      "id": "PROV-1533"
    }
  },
  {
    "patient": {
      "name": "Elijah Renner",
      "id": "PAT-38795"
    },
    "serviceDate": "04/27/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$14570.80",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "7:52 PM"
    },
    "user": "SAD",
    "dateSent": "08/11/2025",
    "dateSentOrig": "08/11/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Rowe Medical Group",
      "id": "PROV-8156"
    }
  },
  {
    "patient": {
      "name": "Sonia Lueilwitz",
      "id": "PAT-49022"
    },
    "serviceDate": "05/04/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$14680.60",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "1:04 AM"
    },
    "user": "CJ",
    "dateSent": "06/19/2025",
    "dateSentOrig": "05/23/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Johnson Medical Group",
      "id": "PROV-7544"
    }
  },
  {
    "patient": {
      "name": "Erick Oberbrunner",
      "id": "PAT-57099"
    },
    "serviceDate": "08/03/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$11921.35",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "10:39 PM"
    },
    "user": "WRK",
    "dateSent": "09/02/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Pacocha Family Practice",
      "id": "PROV-1570"
    }
  },
  {
    "patient": {
      "name": "Norman Harris",
      "id": "PAT-12214"
    },
    "serviceDate": "08/25/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$2734.69",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "9:02 PM"
    },
    "user": "CRD",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Lehner Family Practice",
      "id": "PROV-6416"
    }
  },
  {
    "patient": {
      "name": "Timothy Cartwright",
      "id": "PAT-57058"
    },
    "serviceDate": "04/06/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$6499.62",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "8:35 AM"
    },
    "user": "LGB",
    "dateSent": "08/13/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Schamberger Healthcare",
      "id": "PROV-5139"
    }
  },
  {
    "patient": {
      "name": "Casey Lynch",
      "id": "PAT-32967"
    },
    "serviceDate": "05/24/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$12942.86",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "2:41 PM"
    },
    "user": "EB",
    "dateSent": "07/13/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Cassin Family Practice",
      "id": "PROV-4934"
    }
  },
  {
    "patient": {
      "name": "Mandy O'Conner",
      "id": "PAT-24960"
    },
    "serviceDate": "08/28/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$7322.63",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "9:18 PM"
    },
    "user": "CH",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Kertzmann Clinic",
      "id": "PROV-7286"
    }
  },
  {
    "patient": {
      "name": "Ms. Nadine Hartmann",
      "id": "PAT-39752"
    },
    "serviceDate": "05/21/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$9616.97",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "2:08 AM"
    },
    "user": "RAA",
    "dateSent": "07/03/2025",
    "dateSentOrig": "07/03/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Kulas Clinic",
      "id": "PROV-5313"
    }
  },
  {
    "patient": {
      "name": "Irvin Hand",
      "id": "PAT-74424"
    },
    "serviceDate": "09/01/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$5767.43",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "3:44 AM"
    },
    "user": "GCG",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "McLaughlin Healthcare",
      "id": "PROV-1614"
    }
  },
  {
    "patient": {
      "name": "Ollie Hettinger",
      "id": "PAT-13337"
    },
    "serviceDate": "08/23/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$9390.81",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "12:33 AM"
    },
    "user": "GG",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Rippin Clinic",
      "id": "PROV-4898"
    }
  },
  {
    "patient": {
      "name": "Julia Kuhlman",
      "id": "PAT-60544"
    },
    "serviceDate": "07/09/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$4509.01",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "2:39 PM"
    },
    "user": "AEC",
    "dateSent": "08/15/2025",
    "dateSentOrig": "07/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hilpert Clinic",
      "id": "PROV-8872"
    }
  },
  {
    "patient": {
      "name": "Ellis Trantow-Torphy",
      "id": "PAT-96719"
    },
    "serviceDate": "03/25/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$10643.43",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "10:18 AM"
    },
    "user": "CSH",
    "dateSent": "08/25/2025",
    "dateSentOrig": "04/26/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Fay Clinic",
      "id": "PROV-3280"
    }
  },
  {
    "patient": {
      "name": "Jeremiah Sipes",
      "id": "PAT-13916"
    },
    "serviceDate": "06/05/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$9394.64",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/03/2025",
      "time": "6:04 PM"
    },
    "user": "REM",
    "dateSent": "08/01/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hilll Medical Group",
      "id": "PROV-9795"
    }
  },
  {
    "patient": {
      "name": "Clayton Crooks",
      "id": "PAT-15709"
    },
    "serviceDate": "05/11/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$3027.83",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "5:12 AM"
    },
    "user": "LH",
    "dateSent": "06/01/2025",
    "dateSentOrig": "06/01/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Herman Associates",
      "id": "PROV-5586"
    }
  },
  {
    "patient": {
      "name": "Gordon Turner-Waters",
      "id": "PAT-29429"
    },
    "serviceDate": "03/16/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$10241.99",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "12:09 AM"
    },
    "user": "LJW",
    "dateSent": "06/24/2025",
    "dateSentOrig": "06/10/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Ritchie Associates",
      "id": "PROV-1513"
    }
  },
  {
    "patient": {
      "name": "Karla Robel",
      "id": "PAT-65692"
    },
    "serviceDate": "03/25/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$6849.16",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "5:41 PM"
    },
    "user": "SS",
    "dateSent": "04/10/2025",
    "dateSentOrig": "04/05/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Satterfield-Christiansen Associates",
      "id": "PROV-2695"
    }
  },
  {
    "patient": {
      "name": "Alice Mann",
      "id": "PAT-25385"
    },
    "serviceDate": "06/15/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$1858.83",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "2:29 PM"
    },
    "user": "JAH",
    "dateSent": "06/30/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Runolfsson Healthcare",
      "id": "PROV-4893"
    }
  },
  {
    "patient": {
      "name": "Gwendolyn Rosenbaum DDS",
      "id": "PAT-77230"
    },
    "serviceDate": "06/27/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$6101.80",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "5:18 AM"
    },
    "user": "JES",
    "dateSent": "07/05/2025",
    "dateSentOrig": "06/28/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Schneider Medical Group",
      "id": "PROV-2632"
    }
  },
  {
    "patient": {
      "name": "Adam Medhurst",
      "id": "PAT-12223"
    },
    "serviceDate": "03/07/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$347.66",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "7:07 PM"
    },
    "user": "JJ",
    "dateSent": "03/15/2025",
    "dateSentOrig": "03/15/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Franey Medical Group",
      "id": "PROV-5181"
    }
  },
  {
    "patient": {
      "name": "Floyd Vandervort",
      "id": "PAT-33206"
    },
    "serviceDate": "03/15/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$14657.23",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "11:44 PM"
    },
    "user": "AK",
    "dateSent": "06/26/2025",
    "dateSentOrig": "04/08/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Green Associates",
      "id": "PROV-8648"
    }
  },
  {
    "patient": {
      "name": "Phillip Hermiston",
      "id": "PAT-47725"
    },
    "serviceDate": "08/30/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$6690.18",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "4:08 PM"
    },
    "user": "PRS",
    "dateSent": "09/01/2025",
    "dateSentOrig": "09/01/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Kihn Associates",
      "id": "PROV-9634"
    }
  },
  {
    "patient": {
      "name": "Sheri Greenfelder-Prohaska",
      "id": "PAT-30595"
    },
    "serviceDate": "08/30/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$2414.89",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "6:26 PM"
    },
    "user": "TJ",
    "dateSent": "09/01/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Franecki Family Practice",
      "id": "PROV-5306"
    }
  },
  {
    "patient": {
      "name": "Amy Fahey",
      "id": "PAT-42283"
    },
    "serviceDate": "05/08/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$195.00",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "5:52 AM"
    },
    "user": "WKK",
    "dateSent": "07/11/2025",
    "dateSentOrig": "07/11/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Medhurst Associates",
      "id": "PROV-6620"
    }
  },
  {
    "patient": {
      "name": "Miss Tricia Mante",
      "id": "PAT-29567"
    },
    "serviceDate": "03/15/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$1604.90",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "8:32 AM"
    },
    "user": "FRM",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Marvin Family Practice",
      "id": "PROV-9689"
    }
  },
  {
    "patient": {
      "name": "Tabitha Mann",
      "id": "PAT-58983"
    },
    "serviceDate": "08/12/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$14947.77",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "12:47 AM"
    },
    "user": "SS",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Block Healthcare",
      "id": "PROV-6873"
    }
  },
  {
    "patient": {
      "name": "Ivan Fisher",
      "id": "PAT-60446"
    },
    "serviceDate": "05/10/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$12095.07",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "1:37 AM"
    },
    "user": "RG",
    "dateSent": "08/24/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Brown Clinic",
      "id": "PROV-8045"
    }
  },
  {
    "patient": {
      "name": "Marcia Grady",
      "id": "PAT-99119"
    },
    "serviceDate": "06/11/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$10746.58",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "5:56 PM"
    },
    "user": "HH",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Mraz Healthcare",
      "id": "PROV-2738"
    }
  },
  {
    "patient": {
      "name": "Levi Emmerich",
      "id": "PAT-75566"
    },
    "serviceDate": "05/31/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$7880.23",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "11:21 PM"
    },
    "user": "CEZ",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Schaefer Associates",
      "id": "PROV-8430"
    }
  },
  {
    "patient": {
      "name": "Lisa McCullough",
      "id": "PAT-39915"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$8917.99",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "7:52 PM"
    },
    "user": "KB",
    "dateSent": "06/11/2025",
    "dateSentOrig": "05/26/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Williamson Associates",
      "id": "PROV-6639"
    }
  },
  {
    "patient": {
      "name": "Andrew Quigley",
      "id": "PAT-63759"
    },
    "serviceDate": "07/22/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$9448.53",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "10:14 AM"
    },
    "user": "BRA",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Feil-Parker Medical Group",
      "id": "PROV-9491"
    }
  },
  {
    "patient": {
      "name": "Thelma Dietrich",
      "id": "PAT-83583"
    },
    "serviceDate": "04/23/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$6436.61",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "7:08 AM"
    },
    "user": "PL",
    "dateSent": "05/18/2025",
    "dateSentOrig": "04/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Berge Associates",
      "id": "PROV-1222"
    }
  },
  {
    "patient": {
      "name": "Al Kub",
      "id": "PAT-43373"
    },
    "serviceDate": "06/12/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$8906.44",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "3:32 PM"
    },
    "user": "LTF",
    "dateSent": "07/04/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Wiegand Medical Group",
      "id": "PROV-3455"
    }
  },
  {
    "patient": {
      "name": "Preston Schamberger",
      "id": "PAT-45260"
    },
    "serviceDate": "08/07/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$11256.45",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "10:47 AM"
    },
    "user": "CKZ",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Schaden Healthcare",
      "id": "PROV-2083"
    }
  },
  {
    "patient": {
      "name": "Kristy Green",
      "id": "PAT-35575"
    },
    "serviceDate": "06/25/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$5454.01",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "10:29 PM"
    },
    "user": "DBC",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Stiedemann Family Practice",
      "id": "PROV-9888"
    }
  },
  {
    "patient": {
      "name": "Colin Toy",
      "id": "PAT-74138"
    },
    "serviceDate": "08/08/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$3238.49",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "9:26 AM"
    },
    "user": "JR",
    "dateSent": "08/10/2025",
    "dateSentOrig": "08/10/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Daniel Family Practice",
      "id": "PROV-7330"
    }
  },
  {
    "patient": {
      "name": "Tracey Heaney",
      "id": "PAT-71015"
    },
    "serviceDate": "04/11/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$6601.40",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "3:03 PM"
    },
    "user": "KPS",
    "dateSent": "06/24/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schroeder Associates",
      "id": "PROV-4706"
    }
  },
  {
    "patient": {
      "name": "Sidney Blanda",
      "id": "PAT-93983"
    },
    "serviceDate": "06/26/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$9924.35",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "11:44 PM"
    },
    "user": "LEA",
    "dateSent": "09/01/2025",
    "dateSentOrig": "07/10/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schaden Clinic",
      "id": "PROV-3256"
    }
  },
  {
    "patient": {
      "name": "Dora Blick",
      "id": "PAT-20371"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$9540.43",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "8:12 PM"
    },
    "user": "ESH",
    "dateSent": "07/15/2025",
    "dateSentOrig": "07/15/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Marks Medical Group",
      "id": "PROV-3761"
    }
  },
  {
    "patient": {
      "name": "Mrs. Audrey Leffler",
      "id": "PAT-33703"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$12015.84",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "10:53 PM"
    },
    "user": "BF",
    "dateSent": "07/29/2025",
    "dateSentOrig": "07/19/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Stroman Family Practice",
      "id": "PROV-1131"
    }
  },
  {
    "patient": {
      "name": "Dr. Bob Streich-Sipes",
      "id": "PAT-50239"
    },
    "serviceDate": "05/18/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$8517.44",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "7:01 AM"
    },
    "user": "MN",
    "dateSent": "08/07/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Christiansen Healthcare",
      "id": "PROV-1643"
    }
  },
  {
    "patient": {
      "name": "Glenn Miller",
      "id": "PAT-37584"
    },
    "serviceDate": "03/09/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$6286.77",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "8:15 PM"
    },
    "user": "PB",
    "dateSent": "04/18/2025",
    "dateSentOrig": "04/18/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Adams Healthcare",
      "id": "PROV-6484"
    }
  },
  {
    "patient": {
      "name": "Forrest Dare",
      "id": "PAT-91149"
    },
    "serviceDate": "04/11/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$5897.33",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "4:40 AM"
    },
    "user": "OEV",
    "dateSent": "06/23/2025",
    "dateSentOrig": "06/23/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Tillman Associates",
      "id": "PROV-3197"
    }
  },
  {
    "patient": {
      "name": "Adrian McClure",
      "id": "PAT-89095"
    },
    "serviceDate": "06/30/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$12674.77",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "7:44 AM"
    },
    "user": "MAG",
    "dateSent": "08/22/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Stroman Family Practice",
      "id": "PROV-6208"
    }
  },
  {
    "patient": {
      "name": "Tamara Kihn",
      "id": "PAT-14092"
    },
    "serviceDate": "06/09/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$1666.85",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "4:24 PM"
    },
    "user": "KSH",
    "dateSent": "08/04/2025",
    "dateSentOrig": "08/04/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Rogahn Family Practice",
      "id": "PROV-8090"
    }
  },
  {
    "patient": {
      "name": "Dr. Darrell Sipes",
      "id": "PAT-46511"
    },
    "serviceDate": "05/09/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$7754.88",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/03/2025",
      "time": "1:20 PM"
    },
    "user": "IBR",
    "dateSent": "07/02/2025",
    "dateSentOrig": "07/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Wolf Family Practice",
      "id": "PROV-4723"
    }
  },
  {
    "patient": {
      "name": "Vicki Hammes",
      "id": "PAT-48483"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$7281.47",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "7:27 AM"
    },
    "user": "SB",
    "dateSent": "08/22/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Rogahn Medical Group",
      "id": "PROV-6583"
    }
  },
  {
    "patient": {
      "name": "Al Connelly",
      "id": "PAT-85785"
    },
    "serviceDate": "07/05/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$12657.38",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "3:04 PM"
    },
    "user": "AAB",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Sipes Associates",
      "id": "PROV-8011"
    }
  },
  {
    "patient": {
      "name": "Dianne Bahringer",
      "id": "PAT-70742"
    },
    "serviceDate": "03/10/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$10485.95",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "9:02 AM"
    },
    "user": "MEJ",
    "dateSent": "07/12/2025",
    "dateSentOrig": "07/06/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Grady Healthcare",
      "id": "PROV-3597"
    }
  },
  {
    "patient": {
      "name": "Ethel Johnston V",
      "id": "PAT-65938"
    },
    "serviceDate": "08/13/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$13052.75",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "9:50 PM"
    },
    "user": "NJ",
    "dateSent": "08/14/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ondricka Healthcare",
      "id": "PROV-8282"
    }
  },
  {
    "patient": {
      "name": "Valerie Marquardt",
      "id": "PAT-34019"
    },
    "serviceDate": "07/10/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$8692.35",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "12:04 AM"
    },
    "user": "AL",
    "dateSent": "07/16/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Christiansen Medical Group",
      "id": "PROV-4526"
    }
  },
  {
    "patient": {
      "name": "Ricardo Nicolas",
      "id": "PAT-30351"
    },
    "serviceDate": "03/16/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$1111.65",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "4:51 PM"
    },
    "user": "HK",
    "dateSent": "05/22/2025",
    "dateSentOrig": "05/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Schultz Family Practice",
      "id": "PROV-7980"
    }
  },
  {
    "patient": {
      "name": "Ms. Billie Flatley",
      "id": "PAT-19458"
    },
    "serviceDate": "05/02/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$11287.22",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "4:09 PM"
    },
    "user": "GLS",
    "dateSent": "06/19/2025",
    "dateSentOrig": "05/09/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Yundt Clinic",
      "id": "PROV-4881"
    }
  },
  {
    "patient": {
      "name": "Whitney Hills",
      "id": "PAT-75379"
    },
    "serviceDate": "03/24/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$501.82",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "8:32 PM"
    },
    "user": "BHK",
    "dateSent": "08/30/2025",
    "dateSentOrig": "07/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Collier Healthcare",
      "id": "PROV-3395"
    }
  },
  {
    "patient": {
      "name": "Harry Bernhard Jr.",
      "id": "PAT-82056"
    },
    "serviceDate": "08/01/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$5740.68",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "4:29 AM"
    },
    "user": "LSW",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Runolfsson Associates",
      "id": "PROV-6612"
    }
  },
  {
    "patient": {
      "name": "Mr. Kristopher Stamm",
      "id": "PAT-50496"
    },
    "serviceDate": "04/17/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$1351.73",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "7:48 AM"
    },
    "user": "CGM",
    "dateSent": "04/25/2025",
    "dateSentOrig": "04/25/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kozey-Waters Medical Group",
      "id": "PROV-4568"
    }
  },
  {
    "patient": {
      "name": "Martin O'Kon",
      "id": "PAT-32257"
    },
    "serviceDate": "03/07/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$744.71",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "3:48 PM"
    },
    "user": "AW",
    "dateSent": "04/21/2025",
    "dateSentOrig": "03/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "O'Connell Medical Group",
      "id": "PROV-1694"
    }
  },
  {
    "patient": {
      "name": "Billy Zemlak",
      "id": "PAT-96685"
    },
    "serviceDate": "05/02/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$6609.48",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "2:52 PM"
    },
    "user": "ME",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/19/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Osinski Associates",
      "id": "PROV-4096"
    }
  },
  {
    "patient": {
      "name": "Ollie Pagac",
      "id": "PAT-82276"
    },
    "serviceDate": "07/06/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$1189.25",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "10:34 PM"
    },
    "user": "CAS",
    "dateSent": "07/19/2025",
    "dateSentOrig": "07/19/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Pollich Family Practice",
      "id": "PROV-7854"
    }
  },
  {
    "patient": {
      "name": "Ada Quigley I",
      "id": "PAT-69788"
    },
    "serviceDate": "06/03/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$10602.52",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "8:23 AM"
    },
    "user": "LAH",
    "dateSent": "07/19/2025",
    "dateSentOrig": "06/14/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Hermiston Healthcare",
      "id": "PROV-3877"
    }
  },
  {
    "patient": {
      "name": "Hannah Borer",
      "id": "PAT-57845"
    },
    "serviceDate": "09/01/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$12694.70",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "3:53 PM"
    },
    "user": "DT",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Roberts Healthcare",
      "id": "PROV-7695"
    }
  },
  {
    "patient": {
      "name": "Erika Abbott III",
      "id": "PAT-77816"
    },
    "serviceDate": "07/30/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$8954.74",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "1:13 AM"
    },
    "user": "SQ",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Blanda Medical Group",
      "id": "PROV-3272"
    }
  },
  {
    "patient": {
      "name": "Jared Ruecker",
      "id": "PAT-57668"
    },
    "serviceDate": "05/27/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$4170.51",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "7:00 PM"
    },
    "user": "BS",
    "dateSent": "06/29/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Wunsch Healthcare",
      "id": "PROV-6081"
    }
  },
  {
    "patient": {
      "name": "Damon Schamberger",
      "id": "PAT-40272"
    },
    "serviceDate": "06/07/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$7320.86",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "11:38 AM"
    },
    "user": "CC",
    "dateSent": "07/30/2025",
    "dateSentOrig": "07/07/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ortiz Clinic",
      "id": "PROV-9825"
    }
  },
  {
    "patient": {
      "name": "Frankie Spencer",
      "id": "PAT-78081"
    },
    "serviceDate": "07/19/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$13594.48",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "12:06 PM"
    },
    "user": "DGL",
    "dateSent": "08/04/2025",
    "dateSentOrig": "07/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Stiedemann Associates",
      "id": "PROV-3876"
    }
  },
  {
    "patient": {
      "name": "Ivan Williamson",
      "id": "PAT-98283"
    },
    "serviceDate": "06/26/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$5840.48",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "8:32 AM"
    },
    "user": "CRK",
    "dateSent": "08/13/2025",
    "dateSentOrig": "07/04/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Wehner Clinic",
      "id": "PROV-8868"
    }
  },
  {
    "patient": {
      "name": "Patsy Monahan",
      "id": "PAT-15404"
    },
    "serviceDate": "03/22/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$3290.06",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "5:34 PM"
    },
    "user": "DR",
    "dateSent": "04/09/2025",
    "dateSentOrig": "04/09/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Von Clinic",
      "id": "PROV-3185"
    }
  },
  {
    "patient": {
      "name": "Wilbert Legros",
      "id": "PAT-37987"
    },
    "serviceDate": "08/21/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$14093.17",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "7:38 PM"
    },
    "user": "TL",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Thompson Associates",
      "id": "PROV-9491"
    }
  },
  {
    "patient": {
      "name": "Dr. Victor Hintz",
      "id": "PAT-30614"
    },
    "serviceDate": "04/20/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$9972.94",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "7:25 PM"
    },
    "user": "CEZ",
    "dateSent": "07/21/2025",
    "dateSentOrig": "07/21/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Mitchell Healthcare",
      "id": "PROV-6336"
    }
  },
  {
    "patient": {
      "name": "Jack Schneider",
      "id": "PAT-86538"
    },
    "serviceDate": "03/07/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$7520.16",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "3:26 PM"
    },
    "user": "YSR",
    "dateSent": "04/07/2025",
    "dateSentOrig": "04/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Larkin Family Practice",
      "id": "PROV-1148"
    }
  },
  {
    "patient": {
      "name": "Tracy Tillman",
      "id": "PAT-30822"
    },
    "serviceDate": "03/22/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$4530.53",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "6:22 PM"
    },
    "user": "OK",
    "dateSent": "05/31/2025",
    "dateSentOrig": "04/23/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Sawayn Healthcare",
      "id": "PROV-9959"
    }
  },
  {
    "patient": {
      "name": "Rochelle Nicolas",
      "id": "PAT-76245"
    },
    "serviceDate": "04/13/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$8080.38",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "9:20 AM"
    },
    "user": "VT",
    "dateSent": "08/27/2025",
    "dateSentOrig": "07/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Boehm Clinic",
      "id": "PROV-7934"
    }
  },
  {
    "patient": {
      "name": "Zachary Ratke",
      "id": "PAT-49784"
    },
    "serviceDate": "07/17/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$5078.26",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "1:01 PM"
    },
    "user": "EW",
    "dateSent": "08/14/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Murphy Clinic",
      "id": "PROV-9333"
    }
  },
  {
    "patient": {
      "name": "Tanya Harvey",
      "id": "PAT-67443"
    },
    "serviceDate": "05/18/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$3645.55",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "8:49 AM"
    },
    "user": "ALW",
    "dateSent": "07/10/2025",
    "dateSentOrig": "06/20/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Dietrich Medical Group",
      "id": "PROV-9593"
    }
  },
  {
    "patient": {
      "name": "Dorothy Medhurst-Leffler",
      "id": "PAT-24974"
    },
    "serviceDate": "08/03/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$10272.75",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "9:12 PM"
    },
    "user": "MEH",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/05/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Towne Clinic",
      "id": "PROV-1603"
    }
  },
  {
    "patient": {
      "name": "Melanie DuBuque",
      "id": "PAT-30999"
    },
    "serviceDate": "08/09/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$13616.62",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "7:59 PM"
    },
    "user": "DKH",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Parisian Medical Group",
      "id": "PROV-7350"
    }
  },
  {
    "patient": {
      "name": "Mr. Sean Schimmel",
      "id": "PAT-60685"
    },
    "serviceDate": "08/16/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$606.41",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "7:58 PM"
    },
    "user": "KEP",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Stark Medical Group",
      "id": "PROV-6386"
    }
  },
  {
    "patient": {
      "name": "Hazel Leffler",
      "id": "PAT-10885"
    },
    "serviceDate": "03/26/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$7935.13",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "10:23 PM"
    },
    "user": "BM",
    "dateSent": "06/15/2025",
    "dateSentOrig": "04/07/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Schaden Healthcare",
      "id": "PROV-8024"
    }
  },
  {
    "patient": {
      "name": "Lucille Hyatt",
      "id": "PAT-95158"
    },
    "serviceDate": "06/14/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$14728.46",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "4:38 PM"
    },
    "user": "KF",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Abernathy Associates",
      "id": "PROV-6829"
    }
  },
  {
    "patient": {
      "name": "Ms. Tanya Brekke IV",
      "id": "PAT-98748"
    },
    "serviceDate": "05/18/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$14488.70",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "5:50 PM"
    },
    "user": "EJM",
    "dateSent": "06/26/2025",
    "dateSentOrig": "06/26/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Grant Associates",
      "id": "PROV-8085"
    }
  },
  {
    "patient": {
      "name": "Silvia Parisian III",
      "id": "PAT-95358"
    },
    "serviceDate": "06/30/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$2120.37",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "2:00 AM"
    },
    "user": "LC",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Bartell-Feil Family Practice",
      "id": "PROV-3537"
    }
  },
  {
    "patient": {
      "name": "Ann Nicolas",
      "id": "PAT-84258"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$3919.98",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "8:02 AM"
    },
    "user": "RRL",
    "dateSent": "09/01/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schumm Family Practice",
      "id": "PROV-6091"
    }
  },
  {
    "patient": {
      "name": "Alexandra Marquardt",
      "id": "PAT-53264"
    },
    "serviceDate": "05/29/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$10472.44",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "7:37 AM"
    },
    "user": "AG",
    "dateSent": "07/03/2025",
    "dateSentOrig": "06/08/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Gerhold Associates",
      "id": "PROV-4558"
    }
  },
  {
    "patient": {
      "name": "Julian Nolan",
      "id": "PAT-80703"
    },
    "serviceDate": "07/09/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$14075.88",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "7:33 PM"
    },
    "user": "CY",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Towne Healthcare",
      "id": "PROV-9108"
    }
  },
  {
    "patient": {
      "name": "Claudia Murray",
      "id": "PAT-67872"
    },
    "serviceDate": "06/08/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$10965.70",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "8:52 PM"
    },
    "user": "EHH",
    "dateSent": "07/09/2025",
    "dateSentOrig": "07/09/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Zemlak Family Practice",
      "id": "PROV-7934"
    }
  },
  {
    "patient": {
      "name": "Terrell Runolfsdottir",
      "id": "PAT-94243"
    },
    "serviceDate": "05/14/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$280.55",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "3:14 PM"
    },
    "user": "LRH",
    "dateSent": "07/28/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Wisoky Associates",
      "id": "PROV-2753"
    }
  },
  {
    "patient": {
      "name": "Lucas Schamberger",
      "id": "PAT-26748"
    },
    "serviceDate": "06/20/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$3095.50",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "9:03 AM"
    },
    "user": "ASW",
    "dateSent": "07/23/2025",
    "dateSentOrig": "07/21/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Fahey Medical Group",
      "id": "PROV-9496"
    }
  },
  {
    "patient": {
      "name": "Adrienne Wiegand",
      "id": "PAT-88746"
    },
    "serviceDate": "04/29/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$6368.20",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "10:14 PM"
    },
    "user": "BKB",
    "dateSent": "06/21/2025",
    "dateSentOrig": "05/18/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Wiza Clinic",
      "id": "PROV-6264"
    }
  },
  {
    "patient": {
      "name": "Mr. Keith Stamm",
      "id": "PAT-41267"
    },
    "serviceDate": "04/26/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$11396.74",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "4:57 PM"
    },
    "user": "JT",
    "dateSent": "08/06/2025",
    "dateSentOrig": "06/11/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Pfannerstill Family Practice",
      "id": "PROV-6194"
    }
  },
  {
    "patient": {
      "name": "Terri Daugherty",
      "id": "PAT-23332"
    },
    "serviceDate": "05/25/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$7962.33",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "11:25 AM"
    },
    "user": "TD",
    "dateSent": "07/09/2025",
    "dateSentOrig": "07/09/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Gislason Healthcare",
      "id": "PROV-1526"
    }
  },
  {
    "patient": {
      "name": "Simon Metz",
      "id": "PAT-55030"
    },
    "serviceDate": "03/08/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$746.69",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "4:33 AM"
    },
    "user": "TL",
    "dateSent": "05/25/2025",
    "dateSentOrig": "04/21/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Dibbert Healthcare",
      "id": "PROV-1684"
    }
  },
  {
    "patient": {
      "name": "Nora Nitzsche PhD",
      "id": "PAT-20230"
    },
    "serviceDate": "07/17/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$6034.36",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "5:13 AM"
    },
    "user": "DAW",
    "dateSent": "07/21/2025",
    "dateSentOrig": "07/19/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Mraz Clinic",
      "id": "PROV-3259"
    }
  },
  {
    "patient": {
      "name": "Micheal MacGyver",
      "id": "PAT-46317"
    },
    "serviceDate": "08/15/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$740.78",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "2:32 AM"
    },
    "user": "CB",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Huel Clinic",
      "id": "PROV-9239"
    }
  },
  {
    "patient": {
      "name": "Carla Crona",
      "id": "PAT-28316"
    },
    "serviceDate": "07/02/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$650.66",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "8:06 PM"
    },
    "user": "RL",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Gerhold Medical Group",
      "id": "PROV-3673"
    }
  },
  {
    "patient": {
      "name": "Jill Wunsch V",
      "id": "PAT-54931"
    },
    "serviceDate": "06/21/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$14153.25",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "7:18 AM"
    },
    "user": "SAB",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/10/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Heathcote Clinic",
      "id": "PROV-2077"
    }
  },
  {
    "patient": {
      "name": "Irma Olson DVM",
      "id": "PAT-37089"
    },
    "serviceDate": "04/21/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$13806.36",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "8:57 PM"
    },
    "user": "ASA",
    "dateSent": "07/28/2025",
    "dateSentOrig": "04/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Corkery Family Practice",
      "id": "PROV-6706"
    }
  },
  {
    "patient": {
      "name": "Rolando Haley",
      "id": "PAT-38801"
    },
    "serviceDate": "04/14/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$7120.51",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "1:36 AM"
    },
    "user": "RHR",
    "dateSent": "07/12/2025",
    "dateSentOrig": "07/12/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Konopelski Associates",
      "id": "PROV-9858"
    }
  },
  {
    "patient": {
      "name": "Dr. Wade Schoen",
      "id": "PAT-77356"
    },
    "serviceDate": "06/03/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$10693.83",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "12:02 AM"
    },
    "user": "DN",
    "dateSent": "07/05/2025",
    "dateSentOrig": "07/05/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Goyette Associates",
      "id": "PROV-3033"
    }
  },
  {
    "patient": {
      "name": "Gretchen Kassulke",
      "id": "PAT-45374"
    },
    "serviceDate": "03/14/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$2186.18",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "2:33 PM"
    },
    "user": "JF",
    "dateSent": "05/15/2025",
    "dateSentOrig": "05/08/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Jast Clinic",
      "id": "PROV-2453"
    }
  },
  {
    "patient": {
      "name": "Horace Batz MD",
      "id": "PAT-38243"
    },
    "serviceDate": "06/24/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$7710.97",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "10:58 PM"
    },
    "user": "RAH",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "West Family Practice",
      "id": "PROV-9932"
    }
  },
  {
    "patient": {
      "name": "Tracy Lemke",
      "id": "PAT-57519"
    },
    "serviceDate": "04/16/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$11539.01",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "9:42 AM"
    },
    "user": "AB",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "D'Amore Family Practice",
      "id": "PROV-8912"
    }
  },
  {
    "patient": {
      "name": "Mr. Jermaine Kassulke",
      "id": "PAT-21568"
    },
    "serviceDate": "08/18/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$2091.20",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "6:30 AM"
    },
    "user": "PSP",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Fisher Medical Group",
      "id": "PROV-9386"
    }
  },
  {
    "patient": {
      "name": "Faye Greenfelder",
      "id": "PAT-72380"
    },
    "serviceDate": "03/18/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$11017.93",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "5:29 PM"
    },
    "user": "IL",
    "dateSent": "07/18/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Larkin-Mraz Associates",
      "id": "PROV-2080"
    }
  },
  {
    "patient": {
      "name": "Donald Crooks",
      "id": "PAT-42155"
    },
    "serviceDate": "07/21/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$14958.94",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "10:17 PM"
    },
    "user": "JK",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Volkman Clinic",
      "id": "PROV-1715"
    }
  },
  {
    "patient": {
      "name": "Carl Corwin",
      "id": "PAT-44910"
    },
    "serviceDate": "07/05/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$3297.69",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "11:10 PM"
    },
    "user": "BRM",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Douglas Medical Group",
      "id": "PROV-1652"
    }
  },
  {
    "patient": {
      "name": "Patrick Gutkowski",
      "id": "PAT-57965"
    },
    "serviceDate": "07/25/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$3218.33",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "12:58 PM"
    },
    "user": "AKZ",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schneider Associates",
      "id": "PROV-4533"
    }
  },
  {
    "patient": {
      "name": "Blanche Armstrong",
      "id": "PAT-29488"
    },
    "serviceDate": "07/15/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$14319.56",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "1:39 AM"
    },
    "user": "NR",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Huels Family Practice",
      "id": "PROV-9487"
    }
  },
  {
    "patient": {
      "name": "Margaret Pouros",
      "id": "PAT-66840"
    },
    "serviceDate": "04/17/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$6956.99",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "3:46 AM"
    },
    "user": "MBH",
    "dateSent": "09/01/2025",
    "dateSentOrig": "09/01/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Maggio Clinic",
      "id": "PROV-3773"
    }
  },
  {
    "patient": {
      "name": "Louise Stokes",
      "id": "PAT-80400"
    },
    "serviceDate": "06/08/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$10535.34",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "4:17 AM"
    },
    "user": "EPK",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Bauch Associates",
      "id": "PROV-7815"
    }
  },
  {
    "patient": {
      "name": "Angelo Littel",
      "id": "PAT-53757"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$9833.86",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "8:51 PM"
    },
    "user": "JSW",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Fisher Associates",
      "id": "PROV-2980"
    }
  },
  {
    "patient": {
      "name": "Delia Stroman",
      "id": "PAT-48270"
    },
    "serviceDate": "07/02/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$2775.51",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "11:09 PM"
    },
    "user": "MJY",
    "dateSent": "07/05/2025",
    "dateSentOrig": "07/05/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Douglas Associates",
      "id": "PROV-7165"
    }
  },
  {
    "patient": {
      "name": "Ms. Geneva Ernser",
      "id": "PAT-60704"
    },
    "serviceDate": "05/18/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$13239.09",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "7:13 PM"
    },
    "user": "GH",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Wisozk Medical Group",
      "id": "PROV-2151"
    }
  },
  {
    "patient": {
      "name": "Wanda Considine",
      "id": "PAT-85771"
    },
    "serviceDate": "03/11/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$3178.87",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "7:42 AM"
    },
    "user": "DNJ",
    "dateSent": "06/15/2025",
    "dateSentOrig": "04/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Grimes Medical Group",
      "id": "PROV-1217"
    }
  },
  {
    "patient": {
      "name": "Maryann Daugherty",
      "id": "PAT-92214"
    },
    "serviceDate": "07/28/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$13952.54",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "4:31 PM"
    },
    "user": "BLW",
    "dateSent": "08/01/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Leuschke Family Practice",
      "id": "PROV-6870"
    }
  },
  {
    "patient": {
      "name": "Ms. Natasha Wolff II",
      "id": "PAT-26897"
    },
    "serviceDate": "04/14/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$11163.56",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "6:02 PM"
    },
    "user": "DLK",
    "dateSent": "08/11/2025",
    "dateSentOrig": "04/22/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Corkery Healthcare",
      "id": "PROV-7868"
    }
  },
  {
    "patient": {
      "name": "Elizabeth Aufderhar",
      "id": "PAT-41840"
    },
    "serviceDate": "03/31/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$7063.47",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "8:38 AM"
    },
    "user": "ZS",
    "dateSent": "05/04/2025",
    "dateSentOrig": "05/04/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Gislason Clinic",
      "id": "PROV-7795"
    }
  },
  {
    "patient": {
      "name": "Jaime Denesik",
      "id": "PAT-94446"
    },
    "serviceDate": "07/30/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$1635.83",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "8:38 AM"
    },
    "user": "AC",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "McLaughlin Medical Group",
      "id": "PROV-6070"
    }
  },
  {
    "patient": {
      "name": "Cristina Brakus",
      "id": "PAT-32116"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$8876.59",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "1:27 PM"
    },
    "user": "DK",
    "dateSent": "06/26/2025",
    "dateSentOrig": "06/03/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Glover Medical Group",
      "id": "PROV-1133"
    }
  },
  {
    "patient": {
      "name": "Phillip Rippin",
      "id": "PAT-96621"
    },
    "serviceDate": "03/10/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$8976.10",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "11:28 AM"
    },
    "user": "OPG",
    "dateSent": "07/23/2025",
    "dateSentOrig": "07/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Lowe-Collier Healthcare",
      "id": "PROV-8233"
    }
  },
  {
    "patient": {
      "name": "Karla Hammes PhD",
      "id": "PAT-55501"
    },
    "serviceDate": "04/03/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$9193.08",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "8:35 AM"
    },
    "user": "MQ",
    "dateSent": "07/18/2025",
    "dateSentOrig": "05/31/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Erdman Medical Group",
      "id": "PROV-2679"
    }
  },
  {
    "patient": {
      "name": "Ricky Conroy",
      "id": "PAT-84078"
    },
    "serviceDate": "04/01/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$11724.60",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "2:30 PM"
    },
    "user": "BB",
    "dateSent": "06/08/2025",
    "dateSentOrig": "06/08/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Maggio Family Practice",
      "id": "PROV-2081"
    }
  },
  {
    "patient": {
      "name": "Darren Ziemann",
      "id": "PAT-83704"
    },
    "serviceDate": "06/09/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$4460.90",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "1:43 PM"
    },
    "user": "CO",
    "dateSent": "06/27/2025",
    "dateSentOrig": "06/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Crona Associates",
      "id": "PROV-9755"
    }
  },
  {
    "patient": {
      "name": "Kristine Becker PhD",
      "id": "PAT-43752"
    },
    "serviceDate": "03/17/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$11920.85",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "8:41 PM"
    },
    "user": "CNW",
    "dateSent": "06/11/2025",
    "dateSentOrig": "06/11/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Weimann Family Practice",
      "id": "PROV-7179"
    }
  },
  {
    "patient": {
      "name": "Angela Ankunding III",
      "id": "PAT-52054"
    },
    "serviceDate": "05/21/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$9997.82",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "5:51 AM"
    },
    "user": "IRF",
    "dateSent": "06/07/2025",
    "dateSentOrig": "05/24/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Hayes Associates",
      "id": "PROV-6258"
    }
  },
  {
    "patient": {
      "name": "Ray Hills",
      "id": "PAT-18095"
    },
    "serviceDate": "04/09/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$9406.01",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "3:31 PM"
    },
    "user": "EBC",
    "dateSent": "06/03/2025",
    "dateSentOrig": "05/10/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Marquardt Associates",
      "id": "PROV-7514"
    }
  },
  {
    "patient": {
      "name": "Santos Muller",
      "id": "PAT-48575"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$450.93",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "6:01 PM"
    },
    "user": "JL",
    "dateSent": "07/19/2025",
    "dateSentOrig": "07/12/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Braun Clinic",
      "id": "PROV-3435"
    }
  },
  {
    "patient": {
      "name": "Woodrow Kuhlman",
      "id": "PAT-91924"
    },
    "serviceDate": "04/15/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$4632.69",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "11:19 AM"
    },
    "user": "GAO",
    "dateSent": "04/25/2025",
    "dateSentOrig": "04/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Hartmann-Skiles Associates",
      "id": "PROV-6537"
    }
  },
  {
    "patient": {
      "name": "Bradford Cassin",
      "id": "PAT-11680"
    },
    "serviceDate": "03/16/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$11046.36",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "11:42 PM"
    },
    "user": "NSF",
    "dateSent": "06/26/2025",
    "dateSentOrig": "06/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Runte Medical Group",
      "id": "PROV-5138"
    }
  },
  {
    "patient": {
      "name": "Jeanne Wuckert",
      "id": "PAT-29910"
    },
    "serviceDate": "05/29/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$11952.39",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "1:53 AM"
    },
    "user": "ABB",
    "dateSent": "08/01/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Graham Healthcare",
      "id": "PROV-2852"
    }
  },
  {
    "patient": {
      "name": "Robin Hyatt",
      "id": "PAT-99902"
    },
    "serviceDate": "04/10/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$108.21",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "3:41 PM"
    },
    "user": "JB",
    "dateSent": "06/03/2025",
    "dateSentOrig": "04/20/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Shields Associates",
      "id": "PROV-6211"
    }
  },
  {
    "patient": {
      "name": "Kirk Bechtelar",
      "id": "PAT-99893"
    },
    "serviceDate": "04/20/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$2639.98",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "8:00 AM"
    },
    "user": "AH",
    "dateSent": "05/26/2025",
    "dateSentOrig": "05/26/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Lesch Family Practice",
      "id": "PROV-6667"
    }
  },
  {
    "patient": {
      "name": "Wayne Hessel",
      "id": "PAT-96185"
    },
    "serviceDate": "07/24/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$10604.02",
    "status": "PAID",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "11:49 PM"
    },
    "user": "SB",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hickle Clinic",
      "id": "PROV-6978"
    }
  },
  {
    "patient": {
      "name": "Silvia Lubowitz",
      "id": "PAT-63114"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$13091.29",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "5:01 PM"
    },
    "user": "EK",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Becker Clinic",
      "id": "PROV-7132"
    }
  },
  {
    "patient": {
      "name": "Ms. Marcella Rolfson",
      "id": "PAT-93662"
    },
    "serviceDate": "04/12/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$67.05",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "10:37 AM"
    },
    "user": "MBW",
    "dateSent": "06/26/2025",
    "dateSentOrig": "06/18/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Grant Healthcare",
      "id": "PROV-9750"
    }
  },
  {
    "patient": {
      "name": "Jane Beer",
      "id": "PAT-54187"
    },
    "serviceDate": "04/28/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$11535.39",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "12:57 PM"
    },
    "user": "CLW",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Kutch Family Practice",
      "id": "PROV-4200"
    }
  },
  {
    "patient": {
      "name": "Chelsea Schimmel",
      "id": "PAT-52009"
    },
    "serviceDate": "07/05/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$769.12",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "6:58 AM"
    },
    "user": "OR",
    "dateSent": "08/05/2025",
    "dateSentOrig": "07/10/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Metz Associates",
      "id": "PROV-2126"
    }
  },
  {
    "patient": {
      "name": "Joey Rutherford",
      "id": "PAT-64063"
    },
    "serviceDate": "07/12/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$13063.15",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "5:46 PM"
    },
    "user": "VR",
    "dateSent": "07/18/2025",
    "dateSentOrig": "07/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Abshire Associates",
      "id": "PROV-5843"
    }
  },
  {
    "patient": {
      "name": "Fred Emard",
      "id": "PAT-81890"
    },
    "serviceDate": "04/04/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$9841.76",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "8:05 PM"
    },
    "user": "AT",
    "dateSent": "06/16/2025",
    "dateSentOrig": "05/07/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Dickinson Healthcare",
      "id": "PROV-6308"
    }
  },
  {
    "patient": {
      "name": "Katrina Pacocha-Will",
      "id": "PAT-56091"
    },
    "serviceDate": "08/24/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$4807.32",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "11:39 PM"
    },
    "user": "CK",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Hoppe Family Practice",
      "id": "PROV-4541"
    }
  },
  {
    "patient": {
      "name": "Bridget Aufderhar PhD",
      "id": "PAT-49266"
    },
    "serviceDate": "03/11/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$12520.43",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "3:11 AM"
    },
    "user": "SS",
    "dateSent": "05/02/2025",
    "dateSentOrig": "03/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Pfannerstill Clinic",
      "id": "PROV-5000"
    }
  },
  {
    "patient": {
      "name": "Ada Glover",
      "id": "PAT-85548"
    },
    "serviceDate": "07/12/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$4884.14",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "5:33 AM"
    },
    "user": "EO",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Reilly-Olson Associates",
      "id": "PROV-9804"
    }
  },
  {
    "patient": {
      "name": "Dustin Purdy",
      "id": "PAT-16565"
    },
    "serviceDate": "04/11/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$12553.28",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "8:58 AM"
    },
    "user": "LTS",
    "dateSent": "07/16/2025",
    "dateSentOrig": "07/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Harvey Family Practice",
      "id": "PROV-3722"
    }
  },
  {
    "patient": {
      "name": "Mr. Tracy Spencer",
      "id": "PAT-18181"
    },
    "serviceDate": "05/01/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$14874.71",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "7:31 PM"
    },
    "user": "HS",
    "dateSent": "07/05/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Rodriguez Associates",
      "id": "PROV-8254"
    }
  },
  {
    "patient": {
      "name": "Dr. Amanda Conroy",
      "id": "PAT-48486"
    },
    "serviceDate": "05/25/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$10383.63",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "5:47 PM"
    },
    "user": "RK",
    "dateSent": "07/03/2025",
    "dateSentOrig": "07/03/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Gibson Associates",
      "id": "PROV-2603"
    }
  },
  {
    "patient": {
      "name": "Domingo Emard",
      "id": "PAT-87443"
    },
    "serviceDate": "03/14/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$1895.02",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "11:55 PM"
    },
    "user": "MAH",
    "dateSent": "06/14/2025",
    "dateSentOrig": "04/17/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Cronin Associates",
      "id": "PROV-1865"
    }
  },
  {
    "patient": {
      "name": "Mrs. Velma White",
      "id": "PAT-48892"
    },
    "serviceDate": "03/20/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$3324.64",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "2:15 PM"
    },
    "user": "WCW",
    "dateSent": "05/10/2025",
    "dateSentOrig": "05/10/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Runolfsson Family Practice",
      "id": "PROV-8607"
    }
  },
  {
    "patient": {
      "name": "Antonia Bogan",
      "id": "PAT-23441"
    },
    "serviceDate": "07/24/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$5870.41",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "5:24 AM"
    },
    "user": "KG",
    "dateSent": "08/09/2025",
    "dateSentOrig": "07/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Block Medical Group",
      "id": "PROV-4663"
    }
  },
  {
    "patient": {
      "name": "Sally Thiel",
      "id": "PAT-20360"
    },
    "serviceDate": "06/03/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$4961.67",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "10:34 AM"
    },
    "user": "MAB",
    "dateSent": "08/26/2025",
    "dateSentOrig": "07/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Bartell Clinic",
      "id": "PROV-3206"
    }
  },
  {
    "patient": {
      "name": "Jan Mosciski",
      "id": "PAT-17346"
    },
    "serviceDate": "05/15/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$10217.05",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "10:13 AM"
    },
    "user": "ASP",
    "dateSent": "06/14/2025",
    "dateSentOrig": "05/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Gibson Family Practice",
      "id": "PROV-6032"
    }
  },
  {
    "patient": {
      "name": "Hubert Murphy",
      "id": "PAT-66116"
    },
    "serviceDate": "07/25/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$5021.51",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "8:57 AM"
    },
    "user": "CC",
    "dateSent": "08/15/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Collins Associates",
      "id": "PROV-9795"
    }
  },
  {
    "patient": {
      "name": "Jeffrey Spinka",
      "id": "PAT-45843"
    },
    "serviceDate": "03/11/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$6982.70",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "6:31 AM"
    },
    "user": "AG",
    "dateSent": "06/07/2025",
    "dateSentOrig": "06/07/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ledner Clinic",
      "id": "PROV-7755"
    }
  },
  {
    "patient": {
      "name": "Dr. Carol Larson",
      "id": "PAT-72806"
    },
    "serviceDate": "04/27/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$6473.31",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "2:45 PM"
    },
    "user": "BRC",
    "dateSent": "07/06/2025",
    "dateSentOrig": "05/14/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Johnston Associates",
      "id": "PROV-1588"
    }
  },
  {
    "patient": {
      "name": "Minnie Lindgren V",
      "id": "PAT-19125"
    },
    "serviceDate": "05/02/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$11815.96",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "4:39 PM"
    },
    "user": "TG",
    "dateSent": "08/20/2025",
    "dateSentOrig": "07/09/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Lindgren Healthcare",
      "id": "PROV-3632"
    }
  },
  {
    "patient": {
      "name": "Mr. Rodolfo Willms",
      "id": "PAT-75181"
    },
    "serviceDate": "05/26/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$12294.94",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "3:29 AM"
    },
    "user": "RBL",
    "dateSent": "08/10/2025",
    "dateSentOrig": "07/19/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Langworth Clinic",
      "id": "PROV-2240"
    }
  },
  {
    "patient": {
      "name": "Jasmine Goodwin III",
      "id": "PAT-80828"
    },
    "serviceDate": "06/13/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$11643.21",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "5:19 AM"
    },
    "user": "GBF",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Jacobs Clinic",
      "id": "PROV-2923"
    }
  },
  {
    "patient": {
      "name": "Gina Hermann",
      "id": "PAT-28723"
    },
    "serviceDate": "07/05/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$8654.50",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "6:12 PM"
    },
    "user": "IB",
    "dateSent": "08/08/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Stroman Family Practice",
      "id": "PROV-3539"
    }
  },
  {
    "patient": {
      "name": "Dr. Colleen Keebler",
      "id": "PAT-35706"
    },
    "serviceDate": "07/17/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$9180.13",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "4:19 AM"
    },
    "user": "DNR",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Nikolaus Healthcare",
      "id": "PROV-5052"
    }
  },
  {
    "patient": {
      "name": "Jeanne Barton I",
      "id": "PAT-82074"
    },
    "serviceDate": "04/10/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$10901.13",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "7:48 AM"
    },
    "user": "NB",
    "dateSent": "04/18/2025",
    "dateSentOrig": "04/17/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "MacGyver Family Practice",
      "id": "PROV-1871"
    }
  },
  {
    "patient": {
      "name": "Bryan Runolfsson",
      "id": "PAT-76016"
    },
    "serviceDate": "04/01/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$8224.88",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "11:37 AM"
    },
    "user": "CR",
    "dateSent": "05/22/2025",
    "dateSentOrig": "05/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Kshlerin Clinic",
      "id": "PROV-4063"
    }
  },
  {
    "patient": {
      "name": "Antonia Hyatt Jr.",
      "id": "PAT-78156"
    },
    "serviceDate": "07/06/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$2883.31",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "12:30 PM"
    },
    "user": "GJK",
    "dateSent": "08/11/2025",
    "dateSentOrig": "07/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Rutherford Medical Group",
      "id": "PROV-8848"
    }
  },
  {
    "patient": {
      "name": "Fred Bogisich",
      "id": "PAT-64529"
    },
    "serviceDate": "03/07/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$1914.84",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "11:56 PM"
    },
    "user": "RAL",
    "dateSent": "04/11/2025",
    "dateSentOrig": "03/28/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Effertz Associates",
      "id": "PROV-7471"
    }
  },
  {
    "patient": {
      "name": "Lonnie Jaskolski",
      "id": "PAT-60016"
    },
    "serviceDate": "08/02/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$1639.88",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "9:28 PM"
    },
    "user": "CAW",
    "dateSent": "08/09/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Rolfson Family Practice",
      "id": "PROV-6526"
    }
  },
  {
    "patient": {
      "name": "Rochelle Schuster",
      "id": "PAT-89133"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$5039.42",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "7:20 AM"
    },
    "user": "RM",
    "dateSent": "08/18/2025",
    "dateSentOrig": "07/26/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Fadel Medical Group",
      "id": "PROV-2819"
    }
  },
  {
    "patient": {
      "name": "Lowell Carter-Reilly",
      "id": "PAT-57682"
    },
    "serviceDate": "05/09/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$9621.31",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "7:10 PM"
    },
    "user": "MW",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "O'Keefe Clinic",
      "id": "PROV-2418"
    }
  },
  {
    "patient": {
      "name": "Nichole Hammes",
      "id": "PAT-47687"
    },
    "serviceDate": "03/31/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$2819.70",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "10:16 AM"
    },
    "user": "QNW",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/19/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Stamm Healthcare",
      "id": "PROV-5259"
    }
  },
  {
    "patient": {
      "name": "Dr. Marc Sauer",
      "id": "PAT-13427"
    },
    "serviceDate": "05/07/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$9839.91",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "4:58 PM"
    },
    "user": "DSB",
    "dateSent": "08/04/2025",
    "dateSentOrig": "08/03/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Rath Clinic",
      "id": "PROV-5894"
    }
  },
  {
    "patient": {
      "name": "Bernadette Gusikowski",
      "id": "PAT-61121"
    },
    "serviceDate": "03/07/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$14697.10",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "9:59 PM"
    },
    "user": "ONL",
    "dateSent": "07/05/2025",
    "dateSentOrig": "07/05/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Yundt Family Practice",
      "id": "PROV-1223"
    }
  },
  {
    "patient": {
      "name": "Dr. Melba Collins",
      "id": "PAT-53417"
    },
    "serviceDate": "06/17/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$13619.00",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "7:15 AM"
    },
    "user": "BG",
    "dateSent": "07/18/2025",
    "dateSentOrig": "07/15/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "MacGyver Healthcare",
      "id": "PROV-7719"
    }
  },
  {
    "patient": {
      "name": "Judith Gleason",
      "id": "PAT-52289"
    },
    "serviceDate": "08/25/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$9708.01",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "4:30 AM"
    },
    "user": "AL",
    "dateSent": "08/27/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hoeger Family Practice",
      "id": "PROV-3296"
    }
  },
  {
    "patient": {
      "name": "Kristin Boyle",
      "id": "PAT-46060"
    },
    "serviceDate": "08/16/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$9984.15",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "5:29 AM"
    },
    "user": "KKA",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Russel Healthcare",
      "id": "PROV-4585"
    }
  },
  {
    "patient": {
      "name": "Shannon Johnson",
      "id": "PAT-48889"
    },
    "serviceDate": "05/04/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$8233.61",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "12:08 AM"
    },
    "user": "AC",
    "dateSent": "05/22/2025",
    "dateSentOrig": "05/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Jones Medical Group",
      "id": "PROV-6530"
    }
  },
  {
    "patient": {
      "name": "Judith Zemlak",
      "id": "PAT-48147"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$2594.53",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "9:57 PM"
    },
    "user": "RSB",
    "dateSent": "08/29/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kautzer Medical Group",
      "id": "PROV-1747"
    }
  },
  {
    "patient": {
      "name": "Mrs. Christine Rogahn",
      "id": "PAT-14090"
    },
    "serviceDate": "06/02/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$10874.70",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "4:27 PM"
    },
    "user": "GAO",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Senger Family Practice",
      "id": "PROV-6485"
    }
  },
  {
    "patient": {
      "name": "Emilio Ratke",
      "id": "PAT-50080"
    },
    "serviceDate": "05/04/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$11066.72",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "2:22 PM"
    },
    "user": "JB",
    "dateSent": "05/20/2025",
    "dateSentOrig": "05/20/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hagenes Family Practice",
      "id": "PROV-9432"
    }
  },
  {
    "patient": {
      "name": "Kathryn Mitchell",
      "id": "PAT-20133"
    },
    "serviceDate": "07/11/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$3845.40",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "5:37 AM"
    },
    "user": "VB",
    "dateSent": "08/10/2025",
    "dateSentOrig": "08/10/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Nikolaus Associates",
      "id": "PROV-3593"
    }
  },
  {
    "patient": {
      "name": "Claire Ebert",
      "id": "PAT-17597"
    },
    "serviceDate": "05/10/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$7963.84",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "2:07 AM"
    },
    "user": "JRR",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Wuckert Clinic",
      "id": "PROV-8483"
    }
  },
  {
    "patient": {
      "name": "Alvin Hilll DDS",
      "id": "PAT-43062"
    },
    "serviceDate": "08/02/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$10273.44",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "1:14 AM"
    },
    "user": "IRP",
    "dateSent": "08/13/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hermann Associates",
      "id": "PROV-7470"
    }
  },
  {
    "patient": {
      "name": "Sara Connelly II",
      "id": "PAT-99200"
    },
    "serviceDate": "04/07/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$4892.34",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "5:09 AM"
    },
    "user": "PK",
    "dateSent": "07/18/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Koss Associates",
      "id": "PROV-8875"
    }
  },
  {
    "patient": {
      "name": "Clay Howe",
      "id": "PAT-88257"
    },
    "serviceDate": "06/21/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$11193.31",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "6:43 PM"
    },
    "user": "ARH",
    "dateSent": "07/30/2025",
    "dateSentOrig": "07/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Herzog Family Practice",
      "id": "PROV-6371"
    }
  },
  {
    "patient": {
      "name": "Irma Johnson",
      "id": "PAT-14776"
    },
    "serviceDate": "07/26/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$7926.99",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "4:09 AM"
    },
    "user": "LH",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Franey Clinic",
      "id": "PROV-1065"
    }
  },
  {
    "patient": {
      "name": "Florence Howe",
      "id": "PAT-14336"
    },
    "serviceDate": "05/11/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$11694.23",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "10:52 AM"
    },
    "user": "AH",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/10/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Koss Medical Group",
      "id": "PROV-2936"
    }
  },
  {
    "patient": {
      "name": "Sean Schaefer",
      "id": "PAT-82165"
    },
    "serviceDate": "03/15/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$7298.95",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "6:34 AM"
    },
    "user": "TRW",
    "dateSent": "06/09/2025",
    "dateSentOrig": "06/09/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Stanton-Rowe Associates",
      "id": "PROV-2037"
    }
  },
  {
    "patient": {
      "name": "Mrs. Danielle Huels II",
      "id": "PAT-61774"
    },
    "serviceDate": "04/12/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$1062.65",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "5:55 PM"
    },
    "user": "RED",
    "dateSent": "05/22/2025",
    "dateSentOrig": "05/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Mante Clinic",
      "id": "PROV-7634"
    }
  },
  {
    "patient": {
      "name": "Floyd Auer",
      "id": "PAT-55090"
    },
    "serviceDate": "05/01/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$12096.74",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "12:26 PM"
    },
    "user": "CS",
    "dateSent": "05/20/2025",
    "dateSentOrig": "05/10/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kuhic Associates",
      "id": "PROV-5944"
    }
  },
  {
    "patient": {
      "name": "Fannie Hahn",
      "id": "PAT-40089"
    },
    "serviceDate": "05/05/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$8714.66",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "3:58 PM"
    },
    "user": "DAK",
    "dateSent": "06/09/2025",
    "dateSentOrig": "05/24/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Ferry Healthcare",
      "id": "PROV-9642"
    }
  },
  {
    "patient": {
      "name": "Linda Nikolaus",
      "id": "PAT-97426"
    },
    "serviceDate": "05/18/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$13681.67",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "5:19 AM"
    },
    "user": "JK",
    "dateSent": "07/22/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hintz Associates",
      "id": "PROV-3611"
    }
  },
  {
    "patient": {
      "name": "Randall Prosacco III",
      "id": "PAT-81550"
    },
    "serviceDate": "05/15/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$3495.52",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "11:12 AM"
    },
    "user": "GS",
    "dateSent": "06/25/2025",
    "dateSentOrig": "06/25/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Legros Clinic",
      "id": "PROV-4654"
    }
  },
  {
    "patient": {
      "name": "Dr. Taylor Koelpin",
      "id": "PAT-41672"
    },
    "serviceDate": "03/07/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$3611.94",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "11:58 AM"
    },
    "user": "JSW",
    "dateSent": "04/28/2025",
    "dateSentOrig": "04/05/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Adams Associates",
      "id": "PROV-5735"
    }
  },
  {
    "patient": {
      "name": "Pedro Schultz",
      "id": "PAT-72184"
    },
    "serviceDate": "08/05/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$6721.92",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "7:59 AM"
    },
    "user": "JS",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Stoltenberg Medical Group",
      "id": "PROV-9126"
    }
  },
  {
    "patient": {
      "name": "Carlton Lebsack Jr.",
      "id": "PAT-36732"
    },
    "serviceDate": "03/08/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$652.58",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "1:23 PM"
    },
    "user": "BP",
    "dateSent": "07/12/2025",
    "dateSentOrig": "05/13/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Beier Healthcare",
      "id": "PROV-6239"
    }
  },
  {
    "patient": {
      "name": "Wade Corkery",
      "id": "PAT-71146"
    },
    "serviceDate": "05/12/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$11706.56",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "11:21 PM"
    },
    "user": "PK",
    "dateSent": "07/18/2025",
    "dateSentOrig": "06/09/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Leffler Medical Group",
      "id": "PROV-7068"
    }
  },
  {
    "patient": {
      "name": "Katrina Kiehn",
      "id": "PAT-87357"
    },
    "serviceDate": "04/20/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$14241.75",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "7:24 PM"
    },
    "user": "AQS",
    "dateSent": "08/23/2025",
    "dateSentOrig": "05/31/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Gutkowski Family Practice",
      "id": "PROV-3229"
    }
  },
  {
    "patient": {
      "name": "Doreen Wolff",
      "id": "PAT-76519"
    },
    "serviceDate": "03/26/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$6279.87",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "10:17 AM"
    },
    "user": "DD",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kautzer Clinic",
      "id": "PROV-9590"
    }
  },
  {
    "patient": {
      "name": "Leslie VonRueden",
      "id": "PAT-28256"
    },
    "serviceDate": "05/21/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$3951.72",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "10:29 AM"
    },
    "user": "SRD",
    "dateSent": "08/02/2025",
    "dateSentOrig": "08/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "McClure Family Practice",
      "id": "PROV-3319"
    }
  },
  {
    "patient": {
      "name": "Angelo Hilll",
      "id": "PAT-14741"
    },
    "serviceDate": "03/24/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$13788.09",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "3:25 PM"
    },
    "user": "JK",
    "dateSent": "05/06/2025",
    "dateSentOrig": "05/06/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Schaefer Healthcare",
      "id": "PROV-5343"
    }
  },
  {
    "patient": {
      "name": "Miranda Barton",
      "id": "PAT-58393"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$6689.49",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "2:50 PM"
    },
    "user": "DKA",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Koss Clinic",
      "id": "PROV-2492"
    }
  },
  {
    "patient": {
      "name": "Wm Stanton",
      "id": "PAT-84684"
    },
    "serviceDate": "06/30/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$12092.20",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "2:50 PM"
    },
    "user": "EKD",
    "dateSent": "07/15/2025",
    "dateSentOrig": "07/03/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Swift Associates",
      "id": "PROV-3213"
    }
  },
  {
    "patient": {
      "name": "Miss Nora Heller",
      "id": "PAT-71030"
    },
    "serviceDate": "04/17/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$11020.55",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "10:53 AM"
    },
    "user": "RDK",
    "dateSent": "07/27/2025",
    "dateSentOrig": "07/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Schinner Associates",
      "id": "PROV-2254"
    }
  },
  {
    "patient": {
      "name": "Bruce Monahan",
      "id": "PAT-31174"
    },
    "serviceDate": "04/12/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$6787.69",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "11:31 AM"
    },
    "user": "EC",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Schneider Healthcare",
      "id": "PROV-5922"
    }
  },
  {
    "patient": {
      "name": "Ms. Joan Bruen",
      "id": "PAT-21488"
    },
    "serviceDate": "06/08/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$4361.46",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "2:41 AM"
    },
    "user": "AHM",
    "dateSent": "06/11/2025",
    "dateSentOrig": "06/11/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Funk Healthcare",
      "id": "PROV-8198"
    }
  },
  {
    "patient": {
      "name": "Anna Strosin",
      "id": "PAT-70108"
    },
    "serviceDate": "07/15/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$4281.01",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "12:40 AM"
    },
    "user": "EBW",
    "dateSent": "08/08/2025",
    "dateSentOrig": "07/28/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Volkman Associates",
      "id": "PROV-4662"
    }
  },
  {
    "patient": {
      "name": "Amanda Sawayn",
      "id": "PAT-39550"
    },
    "serviceDate": "05/17/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$12760.48",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "6:05 PM"
    },
    "user": "MT",
    "dateSent": "06/02/2025",
    "dateSentOrig": "05/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Herzog Family Practice",
      "id": "PROV-9658"
    }
  },
  {
    "patient": {
      "name": "Terry Bins",
      "id": "PAT-78794"
    },
    "serviceDate": "06/24/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$8473.22",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "9:39 AM"
    },
    "user": "PMA",
    "dateSent": "08/09/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Mosciski Clinic",
      "id": "PROV-4090"
    }
  },
  {
    "patient": {
      "name": "Gwen McClure",
      "id": "PAT-21898"
    },
    "serviceDate": "07/12/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$14884.46",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "7:28 PM"
    },
    "user": "WB",
    "dateSent": "08/13/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hoppe Associates",
      "id": "PROV-6507"
    }
  },
  {
    "patient": {
      "name": "Mr. Andy Kuvalis",
      "id": "PAT-46097"
    },
    "serviceDate": "03/20/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$12182.94",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "9:28 PM"
    },
    "user": "BR",
    "dateSent": "08/14/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "O'Kon Associates",
      "id": "PROV-7932"
    }
  },
  {
    "patient": {
      "name": "Allison Kunze",
      "id": "PAT-74564"
    },
    "serviceDate": "08/04/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$9828.53",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "4:54 PM"
    },
    "user": "JAT",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hintz Family Practice",
      "id": "PROV-9383"
    }
  },
  {
    "patient": {
      "name": "Nick Heaney-Grady",
      "id": "PAT-64836"
    },
    "serviceDate": "07/16/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$1897.16",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "6:25 AM"
    },
    "user": "SAS",
    "dateSent": "07/31/2025",
    "dateSentOrig": "07/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Ortiz Clinic",
      "id": "PROV-7202"
    }
  },
  {
    "patient": {
      "name": "Aubrey O'Reilly",
      "id": "PAT-30231"
    },
    "serviceDate": "08/06/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$8891.47",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "1:31 AM"
    },
    "user": "MNK",
    "dateSent": "09/02/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Wolf Clinic",
      "id": "PROV-2282"
    }
  },
  {
    "patient": {
      "name": "Doris Kozey",
      "id": "PAT-67128"
    },
    "serviceDate": "06/25/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$12427.78",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "8:46 PM"
    },
    "user": "CSZ",
    "dateSent": "07/09/2025",
    "dateSentOrig": "06/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Koepp Healthcare",
      "id": "PROV-9689"
    }
  },
  {
    "patient": {
      "name": "Melba Greenholt",
      "id": "PAT-42044"
    },
    "serviceDate": "06/15/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$863.12",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "12:18 AM"
    },
    "user": "LRM",
    "dateSent": "08/26/2025",
    "dateSentOrig": "07/03/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Goldner Healthcare",
      "id": "PROV-6820"
    }
  },
  {
    "patient": {
      "name": "Mrs. Teresa Nader",
      "id": "PAT-49752"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$12215.51",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "12:30 PM"
    },
    "user": "MFB",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Glover Associates",
      "id": "PROV-2482"
    }
  },
  {
    "patient": {
      "name": "Eunice Spencer",
      "id": "PAT-13717"
    },
    "serviceDate": "08/16/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$9493.00",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "8:06 AM"
    },
    "user": "HR",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Langworth Healthcare",
      "id": "PROV-4375"
    }
  },
  {
    "patient": {
      "name": "Mr. Dale Robel",
      "id": "PAT-71638"
    },
    "serviceDate": "04/27/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$272.50",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "11:02 AM"
    },
    "user": "GO",
    "dateSent": "07/11/2025",
    "dateSentOrig": "07/11/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "D'Amore Clinic",
      "id": "PROV-6250"
    }
  },
  {
    "patient": {
      "name": "Jonathan Nader",
      "id": "PAT-48429"
    },
    "serviceDate": "08/27/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$6625.19",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "8:10 AM"
    },
    "user": "RO",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Hane Clinic",
      "id": "PROV-4349"
    }
  },
  {
    "patient": {
      "name": "Jonathon Larson",
      "id": "PAT-22529"
    },
    "serviceDate": "04/05/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$6193.81",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "5:55 PM"
    },
    "user": "KK",
    "dateSent": "05/19/2025",
    "dateSentOrig": "05/19/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Toy Family Practice",
      "id": "PROV-7400"
    }
  },
  {
    "patient": {
      "name": "Sidney Koch",
      "id": "PAT-98434"
    },
    "serviceDate": "08/29/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$3060.18",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "2:23 PM"
    },
    "user": "CBK",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Dibbert Family Practice",
      "id": "PROV-1737"
    }
  },
  {
    "patient": {
      "name": "Maureen Torp",
      "id": "PAT-15298"
    },
    "serviceDate": "06/08/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$1922.11",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "11:43 PM"
    },
    "user": "JPW",
    "dateSent": "08/13/2025",
    "dateSentOrig": "06/15/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Hilll Associates",
      "id": "PROV-5905"
    }
  },
  {
    "patient": {
      "name": "John Goldner",
      "id": "PAT-56112"
    },
    "serviceDate": "07/01/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$8843.94",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "9:53 PM"
    },
    "user": "KF",
    "dateSent": "07/18/2025",
    "dateSentOrig": "07/02/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Hudson Associates",
      "id": "PROV-3015"
    }
  },
  {
    "patient": {
      "name": "Norman Gibson",
      "id": "PAT-78159"
    },
    "serviceDate": "05/15/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$1435.29",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "2:51 PM"
    },
    "user": "ED",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Zboncak Clinic",
      "id": "PROV-6579"
    }
  },
  {
    "patient": {
      "name": "Alexis Hodkiewicz",
      "id": "PAT-26143"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$12550.95",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "9:50 AM"
    },
    "user": "FL",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Johnson Family Practice",
      "id": "PROV-4732"
    }
  },
  {
    "patient": {
      "name": "Jeannie Kulas Jr.",
      "id": "PAT-57398"
    },
    "serviceDate": "07/08/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$6672.80",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "4:40 AM"
    },
    "user": "CF",
    "dateSent": "08/07/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Bartoletti Healthcare",
      "id": "PROV-4987"
    }
  },
  {
    "patient": {
      "name": "Antonio Corkery",
      "id": "PAT-46751"
    },
    "serviceDate": "07/29/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$12259.17",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "8:21 AM"
    },
    "user": "ES",
    "dateSent": "08/08/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Reilly Family Practice",
      "id": "PROV-6267"
    }
  },
  {
    "patient": {
      "name": "Adrian Fahey",
      "id": "PAT-22058"
    },
    "serviceDate": "04/18/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$1292.62",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "4:26 AM"
    },
    "user": "BQG",
    "dateSent": "05/31/2025",
    "dateSentOrig": "05/31/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Zemlak Clinic",
      "id": "PROV-5507"
    }
  },
  {
    "patient": {
      "name": "Allan Kunze-O'Reilly",
      "id": "PAT-55620"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$11617.98",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "12:59 PM"
    },
    "user": "GJW",
    "dateSent": "09/01/2025",
    "dateSentOrig": "09/01/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kutch Family Practice",
      "id": "PROV-9282"
    }
  },
  {
    "patient": {
      "name": "Alicia Sipes DVM",
      "id": "PAT-55414"
    },
    "serviceDate": "07/21/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$6412.55",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "12:48 AM"
    },
    "user": "JD",
    "dateSent": "07/22/2025",
    "dateSentOrig": "07/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Simonis Associates",
      "id": "PROV-6733"
    }
  },
  {
    "patient": {
      "name": "Kelvin Strosin",
      "id": "PAT-84253"
    },
    "serviceDate": "06/19/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$2882.29",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "3:50 AM"
    },
    "user": "LW",
    "dateSent": "08/03/2025",
    "dateSentOrig": "07/20/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Koch Clinic",
      "id": "PROV-3252"
    }
  },
  {
    "patient": {
      "name": "Stacey Dicki",
      "id": "PAT-71233"
    },
    "serviceDate": "06/20/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$4162.45",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "8:50 AM"
    },
    "user": "DL",
    "dateSent": "08/24/2025",
    "dateSentOrig": "07/17/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Runte Family Practice",
      "id": "PROV-7379"
    }
  },
  {
    "patient": {
      "name": "Wilbur Quitzon",
      "id": "PAT-69525"
    },
    "serviceDate": "04/24/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$2206.81",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "7:58 PM"
    },
    "user": "WR",
    "dateSent": "05/30/2025",
    "dateSentOrig": "05/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Renner Healthcare",
      "id": "PROV-2464"
    }
  },
  {
    "patient": {
      "name": "Felipe Jerde",
      "id": "PAT-79728"
    },
    "serviceDate": "03/30/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$12847.80",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "9:32 AM"
    },
    "user": "BE",
    "dateSent": "07/20/2025",
    "dateSentOrig": "07/20/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Lindgren Clinic",
      "id": "PROV-1450"
    }
  },
  {
    "patient": {
      "name": "Jo Hamill",
      "id": "PAT-61148"
    },
    "serviceDate": "06/11/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$13383.67",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "9:50 AM"
    },
    "user": "MG",
    "dateSent": "08/02/2025",
    "dateSentOrig": "07/10/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Cronin Healthcare",
      "id": "PROV-5071"
    }
  },
  {
    "patient": {
      "name": "Darrell Reynolds",
      "id": "PAT-67928"
    },
    "serviceDate": "04/11/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$7392.36",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "7:18 PM"
    },
    "user": "DQO",
    "dateSent": "06/23/2025",
    "dateSentOrig": "06/23/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Lang Family Practice",
      "id": "PROV-9924"
    }
  },
  {
    "patient": {
      "name": "Antonia Hermann",
      "id": "PAT-67616"
    },
    "serviceDate": "05/24/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$8566.51",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "10:43 AM"
    },
    "user": "RW",
    "dateSent": "07/02/2025",
    "dateSentOrig": "05/31/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Crona Medical Group",
      "id": "PROV-3610"
    }
  },
  {
    "patient": {
      "name": "Matt Christiansen",
      "id": "PAT-28554"
    },
    "serviceDate": "03/16/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$9173.66",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "10:43 PM"
    },
    "user": "RA",
    "dateSent": "08/13/2025",
    "dateSentOrig": "03/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Hills Medical Group",
      "id": "PROV-6034"
    }
  },
  {
    "patient": {
      "name": "Laverne Doyle II",
      "id": "PAT-61714"
    },
    "serviceDate": "07/31/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$7866.07",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "2:46 PM"
    },
    "user": "HKB",
    "dateSent": "08/14/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Rempel Family Practice",
      "id": "PROV-5217"
    }
  },
  {
    "patient": {
      "name": "Dr. Tyler King",
      "id": "PAT-42757"
    },
    "serviceDate": "08/10/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$1101.05",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "6:44 AM"
    },
    "user": "BGH",
    "dateSent": "08/15/2025",
    "dateSentOrig": "08/11/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Harris Associates",
      "id": "PROV-7173"
    }
  },
  {
    "patient": {
      "name": "Ms. Sheryl Parker",
      "id": "PAT-64339"
    },
    "serviceDate": "03/08/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$2037.27",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "6:43 AM"
    },
    "user": "MW",
    "dateSent": "04/06/2025",
    "dateSentOrig": "03/28/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "McLaughlin Healthcare",
      "id": "PROV-2797"
    }
  },
  {
    "patient": {
      "name": "Darrel Runolfsson-Ankunding",
      "id": "PAT-68075"
    },
    "serviceDate": "05/11/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$6549.85",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "11:49 PM"
    },
    "user": "RS",
    "dateSent": "06/09/2025",
    "dateSentOrig": "06/09/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Parker Healthcare",
      "id": "PROV-9663"
    }
  },
  {
    "patient": {
      "name": "Estelle O'Connell",
      "id": "PAT-52552"
    },
    "serviceDate": "08/17/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$593.25",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "10:37 AM"
    },
    "user": "EAH",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Kutch Medical Group",
      "id": "PROV-6432"
    }
  },
  {
    "patient": {
      "name": "Tami Mertz",
      "id": "PAT-85311"
    },
    "serviceDate": "05/26/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$14779.23",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "1:30 AM"
    },
    "user": "ARL",
    "dateSent": "08/10/2025",
    "dateSentOrig": "08/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schoen Associates",
      "id": "PROV-4272"
    }
  },
  {
    "patient": {
      "name": "Jermaine Smith",
      "id": "PAT-89120"
    },
    "serviceDate": "08/29/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$4128.98",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "11:11 AM"
    },
    "user": "LJH",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/01/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Flatley Associates",
      "id": "PROV-7549"
    }
  },
  {
    "patient": {
      "name": "Sergio Reilly Sr.",
      "id": "PAT-94799"
    },
    "serviceDate": "07/31/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$2589.65",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/04/2025",
      "time": "1:53 AM"
    },
    "user": "MF",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Keebler Associates",
      "id": "PROV-5157"
    }
  },
  {
    "patient": {
      "name": "Melvin Lubowitz",
      "id": "PAT-19796"
    },
    "serviceDate": "03/16/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$4286.89",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "6:44 PM"
    },
    "user": "GDM",
    "dateSent": "07/30/2025",
    "dateSentOrig": "06/14/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Muller Medical Group",
      "id": "PROV-3957"
    }
  },
  {
    "patient": {
      "name": "Herbert Kovacek",
      "id": "PAT-95907"
    },
    "serviceDate": "04/14/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$13579.21",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "10:42 AM"
    },
    "user": "TH",
    "dateSent": "07/24/2025",
    "dateSentOrig": "05/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Flatley Healthcare",
      "id": "PROV-4921"
    }
  },
  {
    "patient": {
      "name": "Mrs. Shari Breitenberg",
      "id": "PAT-73276"
    },
    "serviceDate": "05/15/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$4608.52",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "3:36 PM"
    },
    "user": "LSG",
    "dateSent": "07/04/2025",
    "dateSentOrig": "06/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kutch Medical Group",
      "id": "PROV-7696"
    }
  },
  {
    "patient": {
      "name": "Valerie Kuhn",
      "id": "PAT-56117"
    },
    "serviceDate": "05/28/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$1393.21",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "10:41 PM"
    },
    "user": "OBK",
    "dateSent": "07/18/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Schmeler Associates",
      "id": "PROV-8798"
    }
  },
  {
    "patient": {
      "name": "Beatrice Block",
      "id": "PAT-35906"
    },
    "serviceDate": "08/14/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$7754.93",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "12:26 PM"
    },
    "user": "GB",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Veum-Abshire Medical Group",
      "id": "PROV-5041"
    }
  },
  {
    "patient": {
      "name": "Dr. Roberto Hoeger",
      "id": "PAT-24113"
    },
    "serviceDate": "08/17/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$14766.61",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "6:09 AM"
    },
    "user": "DB",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Collier Family Practice",
      "id": "PROV-7231"
    }
  },
  {
    "patient": {
      "name": "Cary Marvin",
      "id": "PAT-78551"
    },
    "serviceDate": "06/13/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$3538.12",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "9:49 AM"
    },
    "user": "CK",
    "dateSent": "07/30/2025",
    "dateSentOrig": "07/30/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Feest Family Practice",
      "id": "PROV-2238"
    }
  },
  {
    "patient": {
      "name": "Dwight Gorczany DDS",
      "id": "PAT-91089"
    },
    "serviceDate": "08/01/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$575.56",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "3:26 AM"
    },
    "user": "ASB",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Franecki Associates",
      "id": "PROV-6374"
    }
  },
  {
    "patient": {
      "name": "Ada Zemlak",
      "id": "PAT-57203"
    },
    "serviceDate": "06/24/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$10029.83",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "8:34 PM"
    },
    "user": "JER",
    "dateSent": "07/18/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Stiedemann Family Practice",
      "id": "PROV-7065"
    }
  },
  {
    "patient": {
      "name": "Yvonne Fahey PhD",
      "id": "PAT-19718"
    },
    "serviceDate": "08/02/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$9019.86",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "2:01 AM"
    },
    "user": "BAB",
    "dateSent": "08/09/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Wolf Healthcare",
      "id": "PROV-6569"
    }
  },
  {
    "patient": {
      "name": "Olga Rohan-Denesik",
      "id": "PAT-20856"
    },
    "serviceDate": "08/19/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$4037.90",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "1:52 PM"
    },
    "user": "TR",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Schamberger Family Practice",
      "id": "PROV-9315"
    }
  },
  {
    "patient": {
      "name": "Luke Beatty",
      "id": "PAT-95796"
    },
    "serviceDate": "05/23/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$14279.05",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "1:10 PM"
    },
    "user": "LMG",
    "dateSent": "06/20/2025",
    "dateSentOrig": "06/20/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Lemke Medical Group",
      "id": "PROV-2199"
    }
  }
];

export default insuranceClaimsData;

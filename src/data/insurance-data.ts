// Auto-generated insurance claims data
// Generated on: 2025-09-04T05:02:19.865Z
// Total records: 300

import { ClaimRowData } from '../components/ClaimRow';

export const insuranceClaimsData: ClaimRowData[] = [
  {
    "patient": {
      "name": "<PERSON><PERSON>",
      "id": "PAT-73216"
    },
    "serviceDate": "06/10/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$6604.95",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "3:50 PM"
    },
    "user": "CKM",
    "dateSent": "07/01/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Davis Medical Group",
      "id": "PROV-5274"
    }
  },
  {
    "patient": {
      "name": "<PERSON><PERSON> Steuber",
      "id": "PAT-34667"
    },
    "serviceDate": "07/16/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$12666.63",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "5:00 AM"
    },
    "user": "FM",
    "dateSent": "08/07/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Lueilwitz Clinic",
      "id": "PROV-4927"
    }
  },
  {
    "patient": {
      "name": "Martin Mills MD",
      "id": "PAT-55407"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$3089.71",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "6:07 PM"
    },
    "user": "TBH",
    "dateSent": "08/01/2025",
    "dateSentOrig": "07/19/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Kuphal Clinic",
      "id": "PROV-1898"
    }
  },
  {
    "patient": {
      "name": "Arlene Runolfsdottir",
      "id": "PAT-17875"
    },
    "serviceDate": "05/04/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$6705.24",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "10:49 AM"
    },
    "user": "AEB",
    "dateSent": "07/14/2025",
    "dateSentOrig": "05/14/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Mayert Associates",
      "id": "PROV-6050"
    }
  },
  {
    "patient": {
      "name": "Lindsay Nolan",
      "id": "PAT-57397"
    },
    "serviceDate": "07/08/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$1084.81",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "4:17 AM"
    },
    "user": "JKW",
    "dateSent": "07/20/2025",
    "dateSentOrig": "07/20/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Gusikowski Family Practice",
      "id": "PROV-6605"
    }
  },
  {
    "patient": {
      "name": "Lisa Flatley",
      "id": "PAT-97261"
    },
    "serviceDate": "08/01/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$4745.06",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "10:28 PM"
    },
    "user": "CL",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Shields Healthcare",
      "id": "PROV-8426"
    }
  },
  {
    "patient": {
      "name": "Miranda Kunze DVM",
      "id": "PAT-65324"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$450.09",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "12:56 PM"
    },
    "user": "KJQ",
    "dateSent": "09/03/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Renner Clinic",
      "id": "PROV-4234"
    }
  },
  {
    "patient": {
      "name": "Mr. Chad Gutmann",
      "id": "PAT-58746"
    },
    "serviceDate": "06/29/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$10855.89",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "4:52 PM"
    },
    "user": "MB",
    "dateSent": "07/12/2025",
    "dateSentOrig": "07/12/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kunde Healthcare",
      "id": "PROV-5610"
    }
  },
  {
    "patient": {
      "name": "Dr. Chris Will",
      "id": "PAT-91364"
    },
    "serviceDate": "07/16/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$3973.35",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "2:37 AM"
    },
    "user": "ARM",
    "dateSent": "07/17/2025",
    "dateSentOrig": "07/17/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Zulauf Medical Group",
      "id": "PROV-8402"
    }
  },
  {
    "patient": {
      "name": "Lydia Schimmel",
      "id": "PAT-41287"
    },
    "serviceDate": "08/19/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$1735.73",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "9:54 AM"
    },
    "user": "NRG",
    "dateSent": "09/03/2025",
    "dateSentOrig": "09/03/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Bogan Healthcare",
      "id": "PROV-2976"
    }
  },
  {
    "patient": {
      "name": "Elaine Spinka",
      "id": "PAT-98549"
    },
    "serviceDate": "07/20/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$5868.13",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "11:40 PM"
    },
    "user": "ARH",
    "dateSent": "08/02/2025",
    "dateSentOrig": "08/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Ziemann Medical Group",
      "id": "PROV-3817"
    }
  },
  {
    "patient": {
      "name": "Lorraine Gusikowski PhD",
      "id": "PAT-41269"
    },
    "serviceDate": "03/24/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$8914.09",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "5:44 PM"
    },
    "user": "PLN",
    "dateSent": "08/07/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Simonis-Nikolaus Family Practice",
      "id": "PROV-1637"
    }
  },
  {
    "patient": {
      "name": "Jo Cormier",
      "id": "PAT-44499"
    },
    "serviceDate": "06/16/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$10914.36",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "8:36 PM"
    },
    "user": "WS",
    "dateSent": "07/01/2025",
    "dateSentOrig": "07/01/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Hoppe Family Practice",
      "id": "PROV-9450"
    }
  },
  {
    "patient": {
      "name": "Cecilia Wuckert",
      "id": "PAT-80840"
    },
    "serviceDate": "04/06/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$1578.49",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "4:27 AM"
    },
    "user": "OA",
    "dateSent": "06/07/2025",
    "dateSentOrig": "05/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Hayes Clinic",
      "id": "PROV-8840"
    }
  },
  {
    "patient": {
      "name": "Steven Bailey",
      "id": "PAT-24496"
    },
    "serviceDate": "08/23/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$744.95",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "12:40 AM"
    },
    "user": "MF",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Welch Clinic",
      "id": "PROV-4821"
    }
  },
  {
    "patient": {
      "name": "Cecil Aufderhar",
      "id": "PAT-99356"
    },
    "serviceDate": "03/29/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$8003.53",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "8:21 PM"
    },
    "user": "PH",
    "dateSent": "04/18/2025",
    "dateSentOrig": "04/04/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Abshire Healthcare",
      "id": "PROV-9371"
    }
  },
  {
    "patient": {
      "name": "Kristine Upton",
      "id": "PAT-38343"
    },
    "serviceDate": "06/14/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$1387.67",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "1:40 AM"
    },
    "user": "MFG",
    "dateSent": "07/06/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Schowalter Clinic",
      "id": "PROV-1035"
    }
  },
  {
    "patient": {
      "name": "Arnold Dicki",
      "id": "PAT-13971"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$8970.73",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "8:45 AM"
    },
    "user": "TQ",
    "dateSent": "03/30/2025",
    "dateSentOrig": "03/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Dare Associates",
      "id": "PROV-4429"
    }
  },
  {
    "patient": {
      "name": "Stuart Moen",
      "id": "PAT-59314"
    },
    "serviceDate": "07/09/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$719.21",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "2:59 PM"
    },
    "user": "MD",
    "dateSent": "08/11/2025",
    "dateSentOrig": "07/27/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Halvorson Associates",
      "id": "PROV-9145"
    }
  },
  {
    "patient": {
      "name": "Sidney Smitham Jr.",
      "id": "PAT-88568"
    },
    "serviceDate": "09/04/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$8834.75",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "11:23 PM"
    },
    "user": "DH",
    "dateSent": "09/04/2025",
    "dateSentOrig": "09/04/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Beier Associates",
      "id": "PROV-2840"
    }
  },
  {
    "patient": {
      "name": "Pat Schiller",
      "id": "PAT-78847"
    },
    "serviceDate": "07/27/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$4049.92",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "8:55 PM"
    },
    "user": "NAB",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Abshire Medical Group",
      "id": "PROV-4886"
    }
  },
  {
    "patient": {
      "name": "Leslie Wilkinson",
      "id": "PAT-59209"
    },
    "serviceDate": "03/12/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$2605.98",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "1:36 PM"
    },
    "user": "CSK",
    "dateSent": "05/09/2025",
    "dateSentOrig": "05/09/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Schneider Family Practice",
      "id": "PROV-7518"
    }
  },
  {
    "patient": {
      "name": "Mrs. Lindsey Osinski",
      "id": "PAT-39577"
    },
    "serviceDate": "03/12/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$4846.10",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "9:22 PM"
    },
    "user": "EBH",
    "dateSent": "07/25/2025",
    "dateSentOrig": "07/25/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Heaney Healthcare",
      "id": "PROV-3642"
    }
  },
  {
    "patient": {
      "name": "Mr. Bill Tillman",
      "id": "PAT-37556"
    },
    "serviceDate": "05/10/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$14712.74",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "11:43 PM"
    },
    "user": "TRL",
    "dateSent": "07/07/2025",
    "dateSentOrig": "07/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Weissnat Medical Group",
      "id": "PROV-9598"
    }
  },
  {
    "patient": {
      "name": "Laurie Zemlak",
      "id": "PAT-96924"
    },
    "serviceDate": "08/16/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$2515.42",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "6:45 PM"
    },
    "user": "APF",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Schmeler Associates",
      "id": "PROV-4065"
    }
  },
  {
    "patient": {
      "name": "Dr. Kristine Hudson-Kautzer",
      "id": "PAT-37173"
    },
    "serviceDate": "07/03/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$10306.43",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "1:50 PM"
    },
    "user": "AR",
    "dateSent": "08/13/2025",
    "dateSentOrig": "07/20/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Lind-McCullough Medical Group",
      "id": "PROV-7001"
    }
  },
  {
    "patient": {
      "name": "Nathaniel Glover",
      "id": "PAT-18125"
    },
    "serviceDate": "05/03/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$1563.62",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "4:20 PM"
    },
    "user": "JJB",
    "dateSent": "08/29/2025",
    "dateSentOrig": "05/10/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Romaguera Healthcare",
      "id": "PROV-3727"
    }
  },
  {
    "patient": {
      "name": "Brittany Kunze",
      "id": "PAT-41269"
    },
    "serviceDate": "04/01/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$11978.53",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "6:48 PM"
    },
    "user": "SG",
    "dateSent": "08/07/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Bauch-Conn Associates",
      "id": "PROV-2482"
    }
  },
  {
    "patient": {
      "name": "Amber Barton PhD",
      "id": "PAT-34427"
    },
    "serviceDate": "05/02/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$14314.08",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "11:05 AM"
    },
    "user": "MO",
    "dateSent": "05/15/2025",
    "dateSentOrig": "05/13/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Pouros Associates",
      "id": "PROV-7384"
    }
  },
  {
    "patient": {
      "name": "Howard Kohler",
      "id": "PAT-97563"
    },
    "serviceDate": "03/13/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$1913.36",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "6:34 PM"
    },
    "user": "DNR",
    "dateSent": "05/14/2025",
    "dateSentOrig": "04/12/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "VonRueden Medical Group",
      "id": "PROV-3511"
    }
  },
  {
    "patient": {
      "name": "Alfred Lueilwitz",
      "id": "PAT-42921"
    },
    "serviceDate": "03/10/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$8077.32",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "12:29 AM"
    },
    "user": "JF",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Langosh Clinic",
      "id": "PROV-5837"
    }
  },
  {
    "patient": {
      "name": "Gail Schmitt",
      "id": "PAT-72708"
    },
    "serviceDate": "04/08/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$2660.19",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "1:48 AM"
    },
    "user": "AEB",
    "dateSent": "06/07/2025",
    "dateSentOrig": "04/13/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Quigley Family Practice",
      "id": "PROV-1185"
    }
  },
  {
    "patient": {
      "name": "Ricky Kling",
      "id": "PAT-12118"
    },
    "serviceDate": "03/21/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$7948.62",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "11:21 PM"
    },
    "user": "VDV",
    "dateSent": "07/28/2025",
    "dateSentOrig": "07/28/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Leffler Clinic",
      "id": "PROV-4118"
    }
  },
  {
    "patient": {
      "name": "Brent Ziemann",
      "id": "PAT-55979"
    },
    "serviceDate": "03/21/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$13356.63",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "4:04 PM"
    },
    "user": "NW",
    "dateSent": "07/08/2025",
    "dateSentOrig": "07/08/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Johnson Clinic",
      "id": "PROV-7972"
    }
  },
  {
    "patient": {
      "name": "Melinda Ward",
      "id": "PAT-36841"
    },
    "serviceDate": "06/15/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$2517.02",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "6:31 PM"
    },
    "user": "RER",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Murphy Healthcare",
      "id": "PROV-5825"
    }
  },
  {
    "patient": {
      "name": "Antonio Oberbrunner",
      "id": "PAT-29461"
    },
    "serviceDate": "04/19/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$2601.46",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "6:16 PM"
    },
    "user": "ZW",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/10/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Reilly Associates",
      "id": "PROV-8065"
    }
  },
  {
    "patient": {
      "name": "Albert Daniel",
      "id": "PAT-88466"
    },
    "serviceDate": "07/14/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$8428.46",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "12:15 PM"
    },
    "user": "RC",
    "dateSent": "08/23/2025",
    "dateSentOrig": "07/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Bernhard Clinic",
      "id": "PROV-8827"
    }
  },
  {
    "patient": {
      "name": "Melvin Wintheiser",
      "id": "PAT-17941"
    },
    "serviceDate": "07/31/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$2033.57",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "5:40 PM"
    },
    "user": "KJK",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Jacobi Associates",
      "id": "PROV-2365"
    }
  },
  {
    "patient": {
      "name": "Jimmy Wilkinson",
      "id": "PAT-67397"
    },
    "serviceDate": "06/09/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$2836.17",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "9:00 PM"
    },
    "user": "DRG",
    "dateSent": "07/26/2025",
    "dateSentOrig": "06/21/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hansen Medical Group",
      "id": "PROV-7892"
    }
  },
  {
    "patient": {
      "name": "Mrs. Victoria Fay",
      "id": "PAT-15338"
    },
    "serviceDate": "05/08/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$12654.19",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "3:41 AM"
    },
    "user": "ME",
    "dateSent": "09/03/2025",
    "dateSentOrig": "07/11/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Vandervort Clinic",
      "id": "PROV-5029"
    }
  },
  {
    "patient": {
      "name": "Donald Franey",
      "id": "PAT-94482"
    },
    "serviceDate": "04/01/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$8032.25",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "12:47 AM"
    },
    "user": "NFH",
    "dateSent": "04/23/2025",
    "dateSentOrig": "04/23/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Pfannerstill Family Practice",
      "id": "PROV-6829"
    }
  },
  {
    "patient": {
      "name": "Lynette MacGyver",
      "id": "PAT-83594"
    },
    "serviceDate": "08/12/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$11412.02",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "9:19 AM"
    },
    "user": "LDD",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Pagac Family Practice",
      "id": "PROV-8876"
    }
  },
  {
    "patient": {
      "name": "Leona DuBuque-Wintheiser",
      "id": "PAT-79426"
    },
    "serviceDate": "08/17/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$7105.76",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "4:26 PM"
    },
    "user": "GJR",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Crist Family Practice",
      "id": "PROV-4533"
    }
  },
  {
    "patient": {
      "name": "Dora Kuhlman I",
      "id": "PAT-34818"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$13590.24",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "4:45 AM"
    },
    "user": "MG",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Roberts-Thompson Associates",
      "id": "PROV-7937"
    }
  },
  {
    "patient": {
      "name": "Dr. Sergio Blanda DVM",
      "id": "PAT-40210"
    },
    "serviceDate": "09/04/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$7410.81",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "12:54 AM"
    },
    "user": "JAO",
    "dateSent": "09/04/2025",
    "dateSentOrig": "09/04/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Rau-Sauer Healthcare",
      "id": "PROV-9378"
    }
  },
  {
    "patient": {
      "name": "Adrian Bailey-Welch",
      "id": "PAT-42184"
    },
    "serviceDate": "07/17/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$5875.41",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "12:21 AM"
    },
    "user": "KW",
    "dateSent": "08/13/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Blick Medical Group",
      "id": "PROV-9322"
    }
  },
  {
    "patient": {
      "name": "Madeline Lebsack II",
      "id": "PAT-90517"
    },
    "serviceDate": "06/06/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$12332.42",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "12:27 AM"
    },
    "user": "BJG",
    "dateSent": "07/15/2025",
    "dateSentOrig": "07/08/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Marks Family Practice",
      "id": "PROV-2519"
    }
  },
  {
    "patient": {
      "name": "Christian Ondricka",
      "id": "PAT-42975"
    },
    "serviceDate": "06/06/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$996.63",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "3:25 PM"
    },
    "user": "DPH",
    "dateSent": "06/28/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Batz Healthcare",
      "id": "PROV-6715"
    }
  },
  {
    "patient": {
      "name": "Lloyd Schuppe",
      "id": "PAT-91955"
    },
    "serviceDate": "06/18/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$3390.65",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "6:55 PM"
    },
    "user": "JB",
    "dateSent": "09/01/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Halvorson Clinic",
      "id": "PROV-1240"
    }
  },
  {
    "patient": {
      "name": "Gwen Bashirian",
      "id": "PAT-82836"
    },
    "serviceDate": "07/15/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$14390.69",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "9:39 PM"
    },
    "user": "TZ",
    "dateSent": "07/31/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "McGlynn-Deckow Family Practice",
      "id": "PROV-3527"
    }
  },
  {
    "patient": {
      "name": "Cory Cummerata",
      "id": "PAT-31091"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$136.84",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "12:32 PM"
    },
    "user": "SRC",
    "dateSent": "08/10/2025",
    "dateSentOrig": "08/10/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Rempel Medical Group",
      "id": "PROV-5989"
    }
  },
  {
    "patient": {
      "name": "Derrick Sauer",
      "id": "PAT-30192"
    },
    "serviceDate": "06/16/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$9827.59",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "5:46 PM"
    },
    "user": "AW",
    "dateSent": "07/10/2025",
    "dateSentOrig": "07/10/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Moen Medical Group",
      "id": "PROV-8657"
    }
  },
  {
    "patient": {
      "name": "Dr. Hubert Bosco",
      "id": "PAT-91743"
    },
    "serviceDate": "08/24/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$3809.88",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "1:04 PM"
    },
    "user": "MQW",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/26/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Sawayn Clinic",
      "id": "PROV-5945"
    }
  },
  {
    "patient": {
      "name": "Willard Halvorson",
      "id": "PAT-96310"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$5956.42",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "4:23 PM"
    },
    "user": "ACC",
    "dateSent": "06/23/2025",
    "dateSentOrig": "06/23/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Christiansen Family Practice",
      "id": "PROV-5777"
    }
  },
  {
    "patient": {
      "name": "Alma Senger",
      "id": "PAT-50287"
    },
    "serviceDate": "05/10/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$10006.76",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "7:34 PM"
    },
    "user": "DNC",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Schinner Family Practice",
      "id": "PROV-1061"
    }
  },
  {
    "patient": {
      "name": "Dr. Dallas Kirlin",
      "id": "PAT-35187"
    },
    "serviceDate": "04/04/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$13987.76",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "7:47 PM"
    },
    "user": "AS",
    "dateSent": "04/16/2025",
    "dateSentOrig": "04/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Nienow Healthcare",
      "id": "PROV-4334"
    }
  },
  {
    "patient": {
      "name": "Antonio Jerde",
      "id": "PAT-65233"
    },
    "serviceDate": "05/16/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$7974.96",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "2:55 PM"
    },
    "user": "KL",
    "dateSent": "06/06/2025",
    "dateSentOrig": "05/23/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Pollich Associates",
      "id": "PROV-7916"
    }
  },
  {
    "patient": {
      "name": "Emmett Lowe",
      "id": "PAT-37468"
    },
    "serviceDate": "03/16/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$5487.20",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "2:32 PM"
    },
    "user": "TG",
    "dateSent": "03/19/2025",
    "dateSentOrig": "03/19/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Dickinson Clinic",
      "id": "PROV-3319"
    }
  },
  {
    "patient": {
      "name": "Katrina Schuppe",
      "id": "PAT-40666"
    },
    "serviceDate": "04/29/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$1489.14",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "12:32 PM"
    },
    "user": "MSB",
    "dateSent": "05/06/2025",
    "dateSentOrig": "05/06/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Weimann Healthcare",
      "id": "PROV-2764"
    }
  },
  {
    "patient": {
      "name": "Rudy Connelly",
      "id": "PAT-62012"
    },
    "serviceDate": "05/19/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$7062.92",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "12:40 PM"
    },
    "user": "RR",
    "dateSent": "07/08/2025",
    "dateSentOrig": "06/03/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kuphal Family Practice",
      "id": "PROV-4810"
    }
  },
  {
    "patient": {
      "name": "Miss Kelly Moen",
      "id": "PAT-19380"
    },
    "serviceDate": "07/24/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$6924.25",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "5:37 PM"
    },
    "user": "TJ",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/04/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Luettgen-Langosh Clinic",
      "id": "PROV-1319"
    }
  },
  {
    "patient": {
      "name": "Tammy Ward",
      "id": "PAT-73730"
    },
    "serviceDate": "08/11/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$142.03",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "9:29 PM"
    },
    "user": "MO",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Fritsch Healthcare",
      "id": "PROV-8648"
    }
  },
  {
    "patient": {
      "name": "Rufus Mueller",
      "id": "PAT-58305"
    },
    "serviceDate": "04/21/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$7560.31",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "4:58 AM"
    },
    "user": "GSB",
    "dateSent": "07/22/2025",
    "dateSentOrig": "07/22/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Schimmel Healthcare",
      "id": "PROV-2999"
    }
  },
  {
    "patient": {
      "name": "Dr. Jermaine Bernier",
      "id": "PAT-95237"
    },
    "serviceDate": "07/10/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$5934.83",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "5:40 PM"
    },
    "user": "ML",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hudson Medical Group",
      "id": "PROV-6286"
    }
  },
  {
    "patient": {
      "name": "Ms. Nancy Toy",
      "id": "PAT-56148"
    },
    "serviceDate": "08/11/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$1673.71",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "7:25 AM"
    },
    "user": "JD",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Cummerata Healthcare",
      "id": "PROV-9917"
    }
  },
  {
    "patient": {
      "name": "Juanita Mertz I",
      "id": "PAT-63747"
    },
    "serviceDate": "04/30/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$6698.55",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "1:57 PM"
    },
    "user": "JG",
    "dateSent": "08/14/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Parker Associates",
      "id": "PROV-5766"
    }
  },
  {
    "patient": {
      "name": "Carlos Crooks",
      "id": "PAT-70708"
    },
    "serviceDate": "09/02/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$7582.55",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "10:46 AM"
    },
    "user": "HKW",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Lubowitz Healthcare",
      "id": "PROV-7037"
    }
  },
  {
    "patient": {
      "name": "Ms. Verna Leffler",
      "id": "PAT-78030"
    },
    "serviceDate": "05/16/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$2092.62",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "8:24 AM"
    },
    "user": "KP",
    "dateSent": "07/22/2025",
    "dateSentOrig": "06/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Jenkins Clinic",
      "id": "PROV-2653"
    }
  },
  {
    "patient": {
      "name": "Alicia Franey",
      "id": "PAT-41093"
    },
    "serviceDate": "03/11/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$7099.06",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "2:27 AM"
    },
    "user": "CM",
    "dateSent": "08/14/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Maggio Medical Group",
      "id": "PROV-3084"
    }
  },
  {
    "patient": {
      "name": "Dan Ullrich",
      "id": "PAT-55744"
    },
    "serviceDate": "07/31/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$3245.86",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "12:21 AM"
    },
    "user": "MT",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/05/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Cormier Medical Group",
      "id": "PROV-4990"
    }
  },
  {
    "patient": {
      "name": "Lowell Dooley",
      "id": "PAT-42255"
    },
    "serviceDate": "07/28/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$10269.27",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "11:19 PM"
    },
    "user": "HH",
    "dateSent": "08/13/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Cummings Family Practice",
      "id": "PROV-6613"
    }
  },
  {
    "patient": {
      "name": "Timmy Schuster",
      "id": "PAT-28312"
    },
    "serviceDate": "06/06/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$7327.62",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "5:31 PM"
    },
    "user": "AK",
    "dateSent": "07/07/2025",
    "dateSentOrig": "07/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Feest Family Practice",
      "id": "PROV-7316"
    }
  },
  {
    "patient": {
      "name": "Jerome Kreiger",
      "id": "PAT-37068"
    },
    "serviceDate": "08/13/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$5828.52",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "1:24 AM"
    },
    "user": "AT",
    "dateSent": "08/15/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Cronin Medical Group",
      "id": "PROV-7812"
    }
  },
  {
    "patient": {
      "name": "Fredrick Kertzmann",
      "id": "PAT-12987"
    },
    "serviceDate": "03/16/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$14429.51",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "8:55 AM"
    },
    "user": "JR",
    "dateSent": "07/23/2025",
    "dateSentOrig": "07/23/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kilback Clinic",
      "id": "PROV-7583"
    }
  },
  {
    "patient": {
      "name": "Dallas MacGyver",
      "id": "PAT-84165"
    },
    "serviceDate": "08/21/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$6704.64",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "6:51 PM"
    },
    "user": "DM",
    "dateSent": "09/01/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Mayer Associates",
      "id": "PROV-2742"
    }
  },
  {
    "patient": {
      "name": "Trevor Hilll",
      "id": "PAT-47172"
    },
    "serviceDate": "04/20/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$1910.74",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "6:25 PM"
    },
    "user": "SH",
    "dateSent": "08/09/2025",
    "dateSentOrig": "07/03/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Larkin Medical Group",
      "id": "PROV-3763"
    }
  },
  {
    "patient": {
      "name": "Matt O'Keefe",
      "id": "PAT-78790"
    },
    "serviceDate": "06/30/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$9824.68",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/04/2025",
      "time": "6:40 AM"
    },
    "user": "SFW",
    "dateSent": "08/31/2025",
    "dateSentOrig": "07/11/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Stark Clinic",
      "id": "PROV-6939"
    }
  },
  {
    "patient": {
      "name": "Alex Shields",
      "id": "PAT-14676"
    },
    "serviceDate": "04/05/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$10285.71",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "1:19 AM"
    },
    "user": "AJR",
    "dateSent": "07/10/2025",
    "dateSentOrig": "05/11/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Muller Medical Group",
      "id": "PROV-5810"
    }
  },
  {
    "patient": {
      "name": "Rex Gerhold",
      "id": "PAT-44223"
    },
    "serviceDate": "04/10/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$10353.52",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "5:19 AM"
    },
    "user": "CS",
    "dateSent": "04/12/2025",
    "dateSentOrig": "04/12/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Lemke Associates",
      "id": "PROV-5549"
    }
  },
  {
    "patient": {
      "name": "Beatrice Lebsack",
      "id": "PAT-42176"
    },
    "serviceDate": "08/01/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$3226.50",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "12:15 AM"
    },
    "user": "JAA",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/14/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Schumm Clinic",
      "id": "PROV-4176"
    }
  },
  {
    "patient": {
      "name": "Miss Frances Mitchell",
      "id": "PAT-12224"
    },
    "serviceDate": "09/03/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$11858.82",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "10:53 AM"
    },
    "user": "SL",
    "dateSent": "09/04/2025",
    "dateSentOrig": "09/03/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Grady Healthcare",
      "id": "PROV-6299"
    }
  },
  {
    "patient": {
      "name": "Daniel Powlowski PhD",
      "id": "PAT-46821"
    },
    "serviceDate": "06/15/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$14606.49",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "12:53 AM"
    },
    "user": "SD",
    "dateSent": "08/07/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Stanton Healthcare",
      "id": "PROV-9065"
    }
  },
  {
    "patient": {
      "name": "Pete Kuphal",
      "id": "PAT-70050"
    },
    "serviceDate": "03/22/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$2316.43",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "11:56 PM"
    },
    "user": "GB",
    "dateSent": "07/12/2025",
    "dateSentOrig": "07/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Langosh Associates",
      "id": "PROV-4380"
    }
  },
  {
    "patient": {
      "name": "Dr. Ada Auer",
      "id": "PAT-63289"
    },
    "serviceDate": "08/06/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$6257.22",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "8:58 PM"
    },
    "user": "UFN",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Zboncak-Kuphal Associates",
      "id": "PROV-3402"
    }
  },
  {
    "patient": {
      "name": "Ricky Jones IV",
      "id": "PAT-61938"
    },
    "serviceDate": "07/19/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$7035.78",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "7:00 AM"
    },
    "user": "MKH",
    "dateSent": "08/13/2025",
    "dateSentOrig": "08/10/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Abshire Medical Group",
      "id": "PROV-7709"
    }
  },
  {
    "patient": {
      "name": "Alexander Grimes",
      "id": "PAT-33159"
    },
    "serviceDate": "07/03/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$1542.00",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "4:23 AM"
    },
    "user": "CS",
    "dateSent": "08/31/2025",
    "dateSentOrig": "07/03/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Adams Associates",
      "id": "PROV-3600"
    }
  },
  {
    "patient": {
      "name": "Mr. Roman Rolfson",
      "id": "PAT-62330"
    },
    "serviceDate": "03/30/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$8040.76",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "11:04 AM"
    },
    "user": "VPP",
    "dateSent": "07/14/2025",
    "dateSentOrig": "07/14/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Frami Family Practice",
      "id": "PROV-5648"
    }
  },
  {
    "patient": {
      "name": "Phyllis Adams",
      "id": "PAT-53305"
    },
    "serviceDate": "03/24/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$10063.23",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "4:11 AM"
    },
    "user": "EPT",
    "dateSent": "04/22/2025",
    "dateSentOrig": "03/31/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Goodwin Associates",
      "id": "PROV-2688"
    }
  },
  {
    "patient": {
      "name": "Leah Cole",
      "id": "PAT-51205"
    },
    "serviceDate": "08/07/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$7797.37",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "7:51 PM"
    },
    "user": "KS",
    "dateSent": "09/04/2025",
    "dateSentOrig": "09/04/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Abernathy Associates",
      "id": "PROV-2534"
    }
  },
  {
    "patient": {
      "name": "Madeline Wilderman",
      "id": "PAT-97263"
    },
    "serviceDate": "07/30/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$9377.10",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "12:12 PM"
    },
    "user": "SKW",
    "dateSent": "07/30/2025",
    "dateSentOrig": "07/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Wisoky-Bernier Clinic",
      "id": "PROV-9344"
    }
  },
  {
    "patient": {
      "name": "Danielle Pfeffer",
      "id": "PAT-24614"
    },
    "serviceDate": "07/31/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$2515.19",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "10:23 PM"
    },
    "user": "IB",
    "dateSent": "08/02/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Haag Medical Group",
      "id": "PROV-7290"
    }
  },
  {
    "patient": {
      "name": "Bonnie Terry-Ferry",
      "id": "PAT-53002"
    },
    "serviceDate": "08/18/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$1855.32",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "10:22 PM"
    },
    "user": "EDM",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "O'Reilly Clinic",
      "id": "PROV-2106"
    }
  },
  {
    "patient": {
      "name": "Roman Schultz",
      "id": "PAT-57200"
    },
    "serviceDate": "06/28/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$10114.56",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "10:27 AM"
    },
    "user": "CDS",
    "dateSent": "07/01/2025",
    "dateSentOrig": "07/01/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Ondricka Family Practice",
      "id": "PROV-6590"
    }
  },
  {
    "patient": {
      "name": "Deanna Klocko",
      "id": "PAT-48221"
    },
    "serviceDate": "06/08/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$1372.50",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "12:10 AM"
    },
    "user": "LRJ",
    "dateSent": "08/13/2025",
    "dateSentOrig": "07/12/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Collier Clinic",
      "id": "PROV-2563"
    }
  },
  {
    "patient": {
      "name": "Geraldine Quitzon III",
      "id": "PAT-60776"
    },
    "serviceDate": "03/28/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$14753.70",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "5:15 PM"
    },
    "user": "KTE",
    "dateSent": "04/17/2025",
    "dateSentOrig": "04/01/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Beer Medical Group",
      "id": "PROV-3460"
    }
  },
  {
    "patient": {
      "name": "Jack Hessel-Nikolaus",
      "id": "PAT-95142"
    },
    "serviceDate": "05/02/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$13953.26",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "6:19 AM"
    },
    "user": "GRR",
    "dateSent": "06/13/2025",
    "dateSentOrig": "05/17/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kutch-Jast Healthcare",
      "id": "PROV-7042"
    }
  },
  {
    "patient": {
      "name": "Cindy Torp",
      "id": "PAT-99316"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$11269.69",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "5:06 AM"
    },
    "user": "PH",
    "dateSent": "09/01/2025",
    "dateSentOrig": "09/01/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Langosh Healthcare",
      "id": "PROV-7002"
    }
  },
  {
    "patient": {
      "name": "Krystal Medhurst",
      "id": "PAT-98108"
    },
    "serviceDate": "06/09/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$4326.75",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "7:51 AM"
    },
    "user": "AH",
    "dateSent": "09/03/2025",
    "dateSentOrig": "07/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Boehm Medical Group",
      "id": "PROV-5547"
    }
  },
  {
    "patient": {
      "name": "Jimmie Dare",
      "id": "PAT-69755"
    },
    "serviceDate": "08/01/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$4034.79",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "3:52 AM"
    },
    "user": "LR",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/05/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Moore Healthcare",
      "id": "PROV-1595"
    }
  },
  {
    "patient": {
      "name": "Molly Blick",
      "id": "PAT-99624"
    },
    "serviceDate": "08/16/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$11666.89",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "8:45 AM"
    },
    "user": "JAH",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Collins-Harris Family Practice",
      "id": "PROV-5786"
    }
  },
  {
    "patient": {
      "name": "Melissa Schowalter",
      "id": "PAT-64905"
    },
    "serviceDate": "04/14/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$4205.36",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "11:09 AM"
    },
    "user": "LAT",
    "dateSent": "05/25/2025",
    "dateSentOrig": "05/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Pacocha Medical Group",
      "id": "PROV-4556"
    }
  },
  {
    "patient": {
      "name": "Saul Walter",
      "id": "PAT-23940"
    },
    "serviceDate": "05/07/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$4132.20",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "3:41 AM"
    },
    "user": "NSK",
    "dateSent": "08/15/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Goyette Clinic",
      "id": "PROV-1901"
    }
  },
  {
    "patient": {
      "name": "Benny Swift IV",
      "id": "PAT-21905"
    },
    "serviceDate": "03/17/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$688.29",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "10:35 PM"
    },
    "user": "GM",
    "dateSent": "04/15/2025",
    "dateSentOrig": "04/14/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Mayert-Dooley Clinic",
      "id": "PROV-6610"
    }
  },
  {
    "patient": {
      "name": "Archie Langworth",
      "id": "PAT-30194"
    },
    "serviceDate": "04/14/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$3553.67",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "8:02 PM"
    },
    "user": "LL",
    "dateSent": "07/28/2025",
    "dateSentOrig": "05/14/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Jakubowski Healthcare",
      "id": "PROV-7724"
    }
  },
  {
    "patient": {
      "name": "Bradley Dicki",
      "id": "PAT-79536"
    },
    "serviceDate": "05/11/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$11731.07",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "1:32 PM"
    },
    "user": "AG",
    "dateSent": "08/11/2025",
    "dateSentOrig": "08/11/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Kreiger-Lang Family Practice",
      "id": "PROV-5300"
    }
  },
  {
    "patient": {
      "name": "Bonnie Kassulke",
      "id": "PAT-64746"
    },
    "serviceDate": "08/11/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$14007.86",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "5:07 PM"
    },
    "user": "ES",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Rosenbaum Associates",
      "id": "PROV-9187"
    }
  },
  {
    "patient": {
      "name": "Jordan Predovic",
      "id": "PAT-46620"
    },
    "serviceDate": "05/24/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$13305.94",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "11:28 AM"
    },
    "user": "CH",
    "dateSent": "07/31/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Williamson Healthcare",
      "id": "PROV-8961"
    }
  },
  {
    "patient": {
      "name": "Tammy Cole DDS",
      "id": "PAT-44069"
    },
    "serviceDate": "08/08/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$8960.78",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "2:07 AM"
    },
    "user": "JDE",
    "dateSent": "08/14/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Zulauf-Schneider Clinic",
      "id": "PROV-9833"
    }
  },
  {
    "patient": {
      "name": "Craig Koss",
      "id": "PAT-87520"
    },
    "serviceDate": "08/16/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$4157.15",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "1:27 PM"
    },
    "user": "AFS",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schultz Family Practice",
      "id": "PROV-8026"
    }
  },
  {
    "patient": {
      "name": "Gerardo Hessel",
      "id": "PAT-34962"
    },
    "serviceDate": "04/25/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$6672.87",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "11:09 PM"
    },
    "user": "ZC",
    "dateSent": "05/13/2025",
    "dateSentOrig": "05/13/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Zulauf Associates",
      "id": "PROV-8424"
    }
  },
  {
    "patient": {
      "name": "Dr. Dewey Lowe Sr.",
      "id": "PAT-67074"
    },
    "serviceDate": "07/08/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$13706.08",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "7:55 AM"
    },
    "user": "CJH",
    "dateSent": "07/14/2025",
    "dateSentOrig": "07/14/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "McCullough Family Practice",
      "id": "PROV-1595"
    }
  },
  {
    "patient": {
      "name": "Owen Beer",
      "id": "PAT-59738"
    },
    "serviceDate": "08/19/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$3180.16",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "5:51 AM"
    },
    "user": "SBK",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Weissnat Healthcare",
      "id": "PROV-8052"
    }
  },
  {
    "patient": {
      "name": "Darrell Keebler",
      "id": "PAT-65795"
    },
    "serviceDate": "06/25/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$5977.80",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "6:50 AM"
    },
    "user": "EAB",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Schowalter Healthcare",
      "id": "PROV-1679"
    }
  },
  {
    "patient": {
      "name": "Dale Senger PhD",
      "id": "PAT-49467"
    },
    "serviceDate": "05/02/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$9197.06",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "11:44 AM"
    },
    "user": "BP",
    "dateSent": "07/17/2025",
    "dateSentOrig": "06/19/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Adams Medical Group",
      "id": "PROV-9316"
    }
  },
  {
    "patient": {
      "name": "Philip Pollich",
      "id": "PAT-32400"
    },
    "serviceDate": "07/29/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$8180.41",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "7:50 PM"
    },
    "user": "KNR",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/19/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Keeling Clinic",
      "id": "PROV-2249"
    }
  },
  {
    "patient": {
      "name": "May Turner",
      "id": "PAT-60515"
    },
    "serviceDate": "07/31/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$3878.10",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "2:21 AM"
    },
    "user": "JT",
    "dateSent": "08/07/2025",
    "dateSentOrig": "08/07/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Jacobson Healthcare",
      "id": "PROV-7210"
    }
  },
  {
    "patient": {
      "name": "Ron Olson",
      "id": "PAT-63761"
    },
    "serviceDate": "07/15/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$13001.27",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "10:39 AM"
    },
    "user": "APB",
    "dateSent": "08/15/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Lesch Medical Group",
      "id": "PROV-6826"
    }
  },
  {
    "patient": {
      "name": "Bennie Vandervort",
      "id": "PAT-67220"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$1719.87",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "7:12 AM"
    },
    "user": "MNR",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Hirthe Clinic",
      "id": "PROV-9806"
    }
  },
  {
    "patient": {
      "name": "Ira Schroeder",
      "id": "PAT-36758"
    },
    "serviceDate": "07/09/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$5869.83",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "5:07 AM"
    },
    "user": "BRK",
    "dateSent": "07/13/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Runolfsson Clinic",
      "id": "PROV-3338"
    }
  },
  {
    "patient": {
      "name": "Darrell Rath",
      "id": "PAT-56138"
    },
    "serviceDate": "03/17/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$9567.48",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "4:40 AM"
    },
    "user": "AM",
    "dateSent": "03/30/2025",
    "dateSentOrig": "03/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Nitzsche Medical Group",
      "id": "PROV-9090"
    }
  },
  {
    "patient": {
      "name": "Terrell Cruickshank",
      "id": "PAT-40640"
    },
    "serviceDate": "03/16/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$10754.40",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "4:32 AM"
    },
    "user": "WW",
    "dateSent": "05/06/2025",
    "dateSentOrig": "05/06/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Bahringer Family Practice",
      "id": "PROV-6584"
    }
  },
  {
    "patient": {
      "name": "Bruce Romaguera",
      "id": "PAT-95656"
    },
    "serviceDate": "08/21/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$1950.96",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "3:29 PM"
    },
    "user": "ENZ",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Schultz-Waelchi Medical Group",
      "id": "PROV-2708"
    }
  },
  {
    "patient": {
      "name": "Dr. Luke Stanton",
      "id": "PAT-12939"
    },
    "serviceDate": "05/27/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$4303.14",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "11:30 AM"
    },
    "user": "MM",
    "dateSent": "05/29/2025",
    "dateSentOrig": "05/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Stanton Healthcare",
      "id": "PROV-1953"
    }
  },
  {
    "patient": {
      "name": "Pam Langosh MD",
      "id": "PAT-47555"
    },
    "serviceDate": "05/01/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$9853.65",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "2:59 AM"
    },
    "user": "SQG",
    "dateSent": "05/19/2025",
    "dateSentOrig": "05/19/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Schamberger Clinic",
      "id": "PROV-4584"
    }
  },
  {
    "patient": {
      "name": "Pete Kertzmann",
      "id": "PAT-37168"
    },
    "serviceDate": "06/24/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$11870.95",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "4:07 PM"
    },
    "user": "OT",
    "dateSent": "06/26/2025",
    "dateSentOrig": "06/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kris Associates",
      "id": "PROV-4996"
    }
  },
  {
    "patient": {
      "name": "Dr. Karl Grant",
      "id": "PAT-27978"
    },
    "serviceDate": "08/27/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$12696.59",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "8:33 AM"
    },
    "user": "KHS",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Sawayn Associates",
      "id": "PROV-7675"
    }
  },
  {
    "patient": {
      "name": "Jacob Harvey",
      "id": "PAT-85171"
    },
    "serviceDate": "08/17/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$5314.60",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "7:00 AM"
    },
    "user": "JO",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Kuphal Clinic",
      "id": "PROV-3580"
    }
  },
  {
    "patient": {
      "name": "Stacey McCullough",
      "id": "PAT-57897"
    },
    "serviceDate": "03/08/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$6481.95",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "10:14 PM"
    },
    "user": "GDB",
    "dateSent": "07/15/2025",
    "dateSentOrig": "05/24/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Mohr Associates",
      "id": "PROV-5169"
    }
  },
  {
    "patient": {
      "name": "Manuel King",
      "id": "PAT-75493"
    },
    "serviceDate": "07/22/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$7444.27",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "5:44 AM"
    },
    "user": "AG",
    "dateSent": "08/09/2025",
    "dateSentOrig": "08/01/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Prosacco Medical Group",
      "id": "PROV-3840"
    }
  },
  {
    "patient": {
      "name": "Dwayne Mraz",
      "id": "PAT-17655"
    },
    "serviceDate": "06/14/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$4374.54",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "1:10 AM"
    },
    "user": "KW",
    "dateSent": "08/03/2025",
    "dateSentOrig": "08/03/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Ziemann Medical Group",
      "id": "PROV-7182"
    }
  },
  {
    "patient": {
      "name": "David Olson",
      "id": "PAT-71716"
    },
    "serviceDate": "05/24/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$6054.71",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/04/2025",
      "time": "11:37 PM"
    },
    "user": "KS",
    "dateSent": "07/27/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Bechtelar-Walsh Medical Group",
      "id": "PROV-2849"
    }
  },
  {
    "patient": {
      "name": "Earl Sipes",
      "id": "PAT-59005"
    },
    "serviceDate": "06/17/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$5899.27",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "7:49 AM"
    },
    "user": "MH",
    "dateSent": "08/10/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Bogisich Medical Group",
      "id": "PROV-4467"
    }
  },
  {
    "patient": {
      "name": "Geneva Becker",
      "id": "PAT-96152"
    },
    "serviceDate": "04/09/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$6632.75",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "1:28 PM"
    },
    "user": "AAT",
    "dateSent": "05/04/2025",
    "dateSentOrig": "05/04/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Rau Associates",
      "id": "PROV-4548"
    }
  },
  {
    "patient": {
      "name": "Sheryl Hegmann",
      "id": "PAT-23047"
    },
    "serviceDate": "07/27/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$3009.94",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "4:34 AM"
    },
    "user": "DEH",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Blanda Associates",
      "id": "PROV-5131"
    }
  },
  {
    "patient": {
      "name": "Misty Ortiz DVM",
      "id": "PAT-93073"
    },
    "serviceDate": "07/21/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$13088.57",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "10:32 PM"
    },
    "user": "VRM",
    "dateSent": "08/06/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Cormier Healthcare",
      "id": "PROV-3940"
    }
  },
  {
    "patient": {
      "name": "Angelo Schneider",
      "id": "PAT-13504"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$13777.85",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "2:47 AM"
    },
    "user": "LO",
    "dateSent": "07/23/2025",
    "dateSentOrig": "05/28/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Connelly Family Practice",
      "id": "PROV-4806"
    }
  },
  {
    "patient": {
      "name": "Bernadette Rice",
      "id": "PAT-27145"
    },
    "serviceDate": "04/11/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$6431.56",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "6:33 PM"
    },
    "user": "CR",
    "dateSent": "05/29/2025",
    "dateSentOrig": "05/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Fritsch Clinic",
      "id": "PROV-5729"
    }
  },
  {
    "patient": {
      "name": "Edmund Graham",
      "id": "PAT-68158"
    },
    "serviceDate": "03/11/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$12614.91",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "6:38 PM"
    },
    "user": "HN",
    "dateSent": "03/22/2025",
    "dateSentOrig": "03/22/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hilpert-Beier Family Practice",
      "id": "PROV-3971"
    }
  },
  {
    "patient": {
      "name": "Connie Wisoky",
      "id": "PAT-99488"
    },
    "serviceDate": "06/11/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$248.92",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "10:29 AM"
    },
    "user": "EAP",
    "dateSent": "08/06/2025",
    "dateSentOrig": "07/05/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "O'Hara Clinic",
      "id": "PROV-4777"
    }
  },
  {
    "patient": {
      "name": "Brandy Gutkowski",
      "id": "PAT-18048"
    },
    "serviceDate": "08/18/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$13608.09",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "2:41 AM"
    },
    "user": "FJ",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Labadie Associates",
      "id": "PROV-5764"
    }
  },
  {
    "patient": {
      "name": "Tamara Denesik",
      "id": "PAT-27705"
    },
    "serviceDate": "08/19/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$12851.94",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "4:28 PM"
    },
    "user": "JM",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Gutmann Healthcare",
      "id": "PROV-4283"
    }
  },
  {
    "patient": {
      "name": "Ms. Harriet Bergstrom",
      "id": "PAT-14597"
    },
    "serviceDate": "03/28/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$5047.47",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "10:07 AM"
    },
    "user": "JRR",
    "dateSent": "08/03/2025",
    "dateSentOrig": "08/03/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Abshire Medical Group",
      "id": "PROV-3013"
    }
  },
  {
    "patient": {
      "name": "Peter Emmerich",
      "id": "PAT-77198"
    },
    "serviceDate": "03/11/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$3685.63",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "3:38 AM"
    },
    "user": "GAW",
    "dateSent": "05/17/2025",
    "dateSentOrig": "05/17/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Dietrich Family Practice",
      "id": "PROV-1019"
    }
  },
  {
    "patient": {
      "name": "Kathleen Leuschke-Jerde",
      "id": "PAT-78919"
    },
    "serviceDate": "08/17/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$7979.32",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "12:16 AM"
    },
    "user": "CMH",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/27/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Spencer Clinic",
      "id": "PROV-2300"
    }
  },
  {
    "patient": {
      "name": "Silvia King",
      "id": "PAT-69651"
    },
    "serviceDate": "05/30/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$12304.49",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "9:26 AM"
    },
    "user": "EKH",
    "dateSent": "08/26/2025",
    "dateSentOrig": "06/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Rath Medical Group",
      "id": "PROV-8724"
    }
  },
  {
    "patient": {
      "name": "Edmond Lueilwitz",
      "id": "PAT-98442"
    },
    "serviceDate": "07/24/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$5578.78",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "7:48 AM"
    },
    "user": "AP",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Medhurst Healthcare",
      "id": "PROV-4898"
    }
  },
  {
    "patient": {
      "name": "Wayne Goyette",
      "id": "PAT-65363"
    },
    "serviceDate": "03/26/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$7594.75",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "11:31 AM"
    },
    "user": "ABK",
    "dateSent": "04/01/2025",
    "dateSentOrig": "03/31/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Kreiger Medical Group",
      "id": "PROV-3046"
    }
  },
  {
    "patient": {
      "name": "Karen Hayes",
      "id": "PAT-39257"
    },
    "serviceDate": "06/30/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$4745.59",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "1:12 AM"
    },
    "user": "TF",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Pouros Medical Group",
      "id": "PROV-2354"
    }
  },
  {
    "patient": {
      "name": "Eleanor Hudson",
      "id": "PAT-61715"
    },
    "serviceDate": "04/29/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$11079.35",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "11:49 PM"
    },
    "user": "DAG",
    "dateSent": "08/05/2025",
    "dateSentOrig": "08/05/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Jacobs-Larson Associates",
      "id": "PROV-2844"
    }
  },
  {
    "patient": {
      "name": "Milton Fadel",
      "id": "PAT-82971"
    },
    "serviceDate": "05/23/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$4676.55",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "10:34 PM"
    },
    "user": "RK",
    "dateSent": "08/17/2025",
    "dateSentOrig": "07/01/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Tremblay Medical Group",
      "id": "PROV-1977"
    }
  },
  {
    "patient": {
      "name": "Dr. Bobbie Hamill",
      "id": "PAT-84180"
    },
    "serviceDate": "04/02/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$5699.79",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "11:22 PM"
    },
    "user": "QW",
    "dateSent": "06/04/2025",
    "dateSentOrig": "06/02/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Spencer Associates",
      "id": "PROV-7859"
    }
  },
  {
    "patient": {
      "name": "Georgia Monahan",
      "id": "PAT-74625"
    },
    "serviceDate": "07/06/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$11506.72",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "10:19 PM"
    },
    "user": "JQW",
    "dateSent": "08/15/2025",
    "dateSentOrig": "08/15/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Bosco Family Practice",
      "id": "PROV-3162"
    }
  },
  {
    "patient": {
      "name": "Lindsey Howe",
      "id": "PAT-20324"
    },
    "serviceDate": "08/17/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$5457.31",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "11:07 AM"
    },
    "user": "MB",
    "dateSent": "08/20/2025",
    "dateSentOrig": "08/20/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Wolff Healthcare",
      "id": "PROV-5206"
    }
  },
  {
    "patient": {
      "name": "Lorraine Davis",
      "id": "PAT-14008"
    },
    "serviceDate": "07/08/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$1906.15",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "2:23 AM"
    },
    "user": "CRK",
    "dateSent": "08/21/2025",
    "dateSentOrig": "08/21/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Ankunding Healthcare",
      "id": "PROV-1876"
    }
  },
  {
    "patient": {
      "name": "Mr. Greg Ortiz III",
      "id": "PAT-62401"
    },
    "serviceDate": "03/18/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$5048.83",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "11:38 PM"
    },
    "user": "STC",
    "dateSent": "04/11/2025",
    "dateSentOrig": "03/27/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Gorczany Associates",
      "id": "PROV-1639"
    }
  },
  {
    "patient": {
      "name": "Miss Minnie Wilkinson",
      "id": "PAT-60433"
    },
    "serviceDate": "05/24/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$1327.97",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "2:33 AM"
    },
    "user": "CM",
    "dateSent": "07/22/2025",
    "dateSentOrig": "06/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Klocko Medical Group",
      "id": "PROV-3215"
    }
  },
  {
    "patient": {
      "name": "Rogelio Abernathy",
      "id": "PAT-96824"
    },
    "serviceDate": "06/03/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$11404.43",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "2:33 PM"
    },
    "user": "VB",
    "dateSent": "06/07/2025",
    "dateSentOrig": "06/07/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Halvorson Medical Group",
      "id": "PROV-2941"
    }
  },
  {
    "patient": {
      "name": "Miss Kelli Quigley",
      "id": "PAT-64598"
    },
    "serviceDate": "06/26/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$5594.86",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/07/2025",
      "time": "10:07 PM"
    },
    "user": "RPB",
    "dateSent": "07/20/2025",
    "dateSentOrig": "07/20/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kihn Associates",
      "id": "PROV-6186"
    }
  },
  {
    "patient": {
      "name": "Veronica Lesch",
      "id": "PAT-82557"
    },
    "serviceDate": "08/14/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$1544.53",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/05/2025",
      "time": "1:36 PM"
    },
    "user": "AKH",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Schumm Associates",
      "id": "PROV-2586"
    }
  },
  {
    "patient": {
      "name": "Spencer Yost-Lindgren",
      "id": "PAT-68788"
    },
    "serviceDate": "04/15/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$8946.28",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "7:56 AM"
    },
    "user": "EC",
    "dateSent": "08/12/2025",
    "dateSentOrig": "06/28/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kulas Healthcare",
      "id": "PROV-4486"
    }
  },
  {
    "patient": {
      "name": "Dr. Terrance Cummerata",
      "id": "PAT-81163"
    },
    "serviceDate": "05/16/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$13244.67",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "8:29 AM"
    },
    "user": "MB",
    "dateSent": "05/21/2025",
    "dateSentOrig": "05/21/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Pollich Family Practice",
      "id": "PROV-9185"
    }
  },
  {
    "patient": {
      "name": "Tony Prohaska",
      "id": "PAT-53178"
    },
    "serviceDate": "04/06/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$7479.19",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "12:14 PM"
    },
    "user": "JAB",
    "dateSent": "06/20/2025",
    "dateSentOrig": "04/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Fisher Medical Group",
      "id": "PROV-9348"
    }
  },
  {
    "patient": {
      "name": "Phillip Kuvalis",
      "id": "PAT-72940"
    },
    "serviceDate": "03/23/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$1455.22",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "3:46 AM"
    },
    "user": "ABE",
    "dateSent": "07/05/2025",
    "dateSentOrig": "05/08/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Hegmann Medical Group",
      "id": "PROV-7461"
    }
  },
  {
    "patient": {
      "name": "Dolores Lynch",
      "id": "PAT-13202"
    },
    "serviceDate": "06/23/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$6943.31",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "2:40 PM"
    },
    "user": "FD",
    "dateSent": "07/12/2025",
    "dateSentOrig": "07/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Johns-Kemmer Clinic",
      "id": "PROV-3770"
    }
  },
  {
    "patient": {
      "name": "Rufus Muller",
      "id": "PAT-72956"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$6966.17",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "6:41 AM"
    },
    "user": "VF",
    "dateSent": "08/07/2025",
    "dateSentOrig": "07/17/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Beahan Family Practice",
      "id": "PROV-5087"
    }
  },
  {
    "patient": {
      "name": "Felicia Quigley",
      "id": "PAT-93614"
    },
    "serviceDate": "07/11/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$1850.65",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "7:11 PM"
    },
    "user": "KSR",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/10/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Hyatt Associates",
      "id": "PROV-1777"
    }
  },
  {
    "patient": {
      "name": "Elvira Bauch",
      "id": "PAT-42780"
    },
    "serviceDate": "06/30/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$5751.01",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "2:06 PM"
    },
    "user": "TB",
    "dateSent": "07/12/2025",
    "dateSentOrig": "07/12/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Jenkins Clinic",
      "id": "PROV-9293"
    }
  },
  {
    "patient": {
      "name": "Ms. Terri DuBuque",
      "id": "PAT-79694"
    },
    "serviceDate": "05/29/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$10108.25",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "4:45 PM"
    },
    "user": "FAS",
    "dateSent": "07/27/2025",
    "dateSentOrig": "07/19/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Barton Clinic",
      "id": "PROV-9290"
    }
  },
  {
    "patient": {
      "name": "Neil Beier",
      "id": "PAT-50404"
    },
    "serviceDate": "04/09/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$6216.66",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "7:47 AM"
    },
    "user": "BAA",
    "dateSent": "05/28/2025",
    "dateSentOrig": "05/28/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Klein Family Practice",
      "id": "PROV-1449"
    }
  },
  {
    "patient": {
      "name": "Wendy Carter",
      "id": "PAT-83216"
    },
    "serviceDate": "08/24/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$2684.74",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "12:50 PM"
    },
    "user": "JAW",
    "dateSent": "09/03/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Sipes Medical Group",
      "id": "PROV-7119"
    }
  },
  {
    "patient": {
      "name": "Dr. Randall Kunde",
      "id": "PAT-29581"
    },
    "serviceDate": "07/21/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$10970.73",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "12:10 AM"
    },
    "user": "GJJ",
    "dateSent": "07/23/2025",
    "dateSentOrig": "07/22/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Stracke Medical Group",
      "id": "PROV-4724"
    }
  },
  {
    "patient": {
      "name": "Louis Lueilwitz",
      "id": "PAT-60718"
    },
    "serviceDate": "04/02/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$14268.94",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "9:23 PM"
    },
    "user": "RS",
    "dateSent": "07/15/2025",
    "dateSentOrig": "07/09/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Romaguera Medical Group",
      "id": "PROV-7527"
    }
  },
  {
    "patient": {
      "name": "Bridget Gulgowski",
      "id": "PAT-63904"
    },
    "serviceDate": "03/19/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$2845.25",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "2:33 PM"
    },
    "user": "MSS",
    "dateSent": "05/08/2025",
    "dateSentOrig": "05/08/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Robel Associates",
      "id": "PROV-5250"
    }
  },
  {
    "patient": {
      "name": "Sonya Kiehn III",
      "id": "PAT-16325"
    },
    "serviceDate": "05/16/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$10876.13",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "6:31 AM"
    },
    "user": "LH",
    "dateSent": "09/01/2025",
    "dateSentOrig": "09/01/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Considine Medical Group",
      "id": "PROV-3003"
    }
  },
  {
    "patient": {
      "name": "Rochelle Parker",
      "id": "PAT-29897"
    },
    "serviceDate": "05/19/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$8256.24",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "1:17 PM"
    },
    "user": "CSB",
    "dateSent": "05/27/2025",
    "dateSentOrig": "05/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Brown Associates",
      "id": "PROV-2702"
    }
  },
  {
    "patient": {
      "name": "Curtis Roob",
      "id": "PAT-77799"
    },
    "serviceDate": "08/31/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$10822.89",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "11:23 AM"
    },
    "user": "SCJ",
    "dateSent": "09/01/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Zieme Family Practice",
      "id": "PROV-2817"
    }
  },
  {
    "patient": {
      "name": "Dana Ebert",
      "id": "PAT-99620"
    },
    "serviceDate": "04/22/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$6401.28",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "8:26 AM"
    },
    "user": "JH",
    "dateSent": "06/26/2025",
    "dateSentOrig": "05/05/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Cruickshank Family Practice",
      "id": "PROV-3193"
    }
  },
  {
    "patient": {
      "name": "Thomas Moore",
      "id": "PAT-20089"
    },
    "serviceDate": "03/29/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$10048.95",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "9:23 PM"
    },
    "user": "EH",
    "dateSent": "04/21/2025",
    "dateSentOrig": "04/21/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Padberg-Hudson Healthcare",
      "id": "PROV-6077"
    }
  },
  {
    "patient": {
      "name": "Kathleen Klein",
      "id": "PAT-87907"
    },
    "serviceDate": "07/09/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$8951.82",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "4:57 PM"
    },
    "user": "ASK",
    "dateSent": "07/25/2025",
    "dateSentOrig": "07/15/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Flatley Associates",
      "id": "PROV-8389"
    }
  },
  {
    "patient": {
      "name": "Kenneth Schamberger Jr.",
      "id": "PAT-42498"
    },
    "serviceDate": "03/13/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$2103.78",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "9:18 AM"
    },
    "user": "CKL",
    "dateSent": "08/14/2025",
    "dateSentOrig": "06/15/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Fritsch Associates",
      "id": "PROV-8823"
    }
  },
  {
    "patient": {
      "name": "Vera Kuhn",
      "id": "PAT-31219"
    },
    "serviceDate": "05/29/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$12579.97",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "9:55 AM"
    },
    "user": "KRN",
    "dateSent": "08/19/2025",
    "dateSentOrig": "08/19/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Murphy Associates",
      "id": "PROV-2036"
    }
  },
  {
    "patient": {
      "name": "Walter Nolan",
      "id": "PAT-88050"
    },
    "serviceDate": "07/20/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$2430.31",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "3:55 PM"
    },
    "user": "AD",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Fahey Associates",
      "id": "PROV-3920"
    }
  },
  {
    "patient": {
      "name": "Roberta Jast",
      "id": "PAT-60097"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$14697.07",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "8:47 PM"
    },
    "user": "OM",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Ondricka Family Practice",
      "id": "PROV-2356"
    }
  },
  {
    "patient": {
      "name": "Elijah Stamm",
      "id": "PAT-99631"
    },
    "serviceDate": "05/18/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$6657.73",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "12:06 PM"
    },
    "user": "ML",
    "dateSent": "09/03/2025",
    "dateSentOrig": "09/03/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Kuphal Associates",
      "id": "PROV-3148"
    }
  },
  {
    "patient": {
      "name": "Bradley Simonis",
      "id": "PAT-91062"
    },
    "serviceDate": "04/23/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$10452.38",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "9:41 PM"
    },
    "user": "KRB",
    "dateSent": "05/02/2025",
    "dateSentOrig": "04/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Gutmann Associates",
      "id": "PROV-8864"
    }
  },
  {
    "patient": {
      "name": "Janet Cormier",
      "id": "PAT-32190"
    },
    "serviceDate": "08/03/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$461.20",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "7:58 AM"
    },
    "user": "IW",
    "dateSent": "08/04/2025",
    "dateSentOrig": "08/04/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "O'Conner Family Practice",
      "id": "PROV-3958"
    }
  },
  {
    "patient": {
      "name": "Amber Harber",
      "id": "PAT-12669"
    },
    "serviceDate": "07/28/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$3263.48",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "5:54 PM"
    },
    "user": "CB",
    "dateSent": "08/03/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Carter Healthcare",
      "id": "PROV-7982"
    }
  },
  {
    "patient": {
      "name": "Joyce Rippin",
      "id": "PAT-39372"
    },
    "serviceDate": "05/30/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$7838.20",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "5:32 AM"
    },
    "user": "IDH",
    "dateSent": "08/23/2025",
    "dateSentOrig": "08/23/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Marquardt Medical Group",
      "id": "PROV-6307"
    }
  },
  {
    "patient": {
      "name": "Miss Doris Dickens",
      "id": "PAT-10363"
    },
    "serviceDate": "05/21/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$11422.02",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "12:35 PM"
    },
    "user": "FHO",
    "dateSent": "05/22/2025",
    "dateSentOrig": "05/22/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Berge Healthcare",
      "id": "PROV-8891"
    }
  },
  {
    "patient": {
      "name": "Ms. Shari Beier",
      "id": "PAT-15858"
    },
    "serviceDate": "08/25/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$9091.99",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "10:37 PM"
    },
    "user": "JPO",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Sipes Clinic",
      "id": "PROV-7015"
    }
  },
  {
    "patient": {
      "name": "Kari Dicki I",
      "id": "PAT-41974"
    },
    "serviceDate": "08/03/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$3904.16",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "11:52 AM"
    },
    "user": "EG",
    "dateSent": "08/11/2025",
    "dateSentOrig": "08/11/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Rice Medical Group",
      "id": "PROV-6343"
    }
  },
  {
    "patient": {
      "name": "Miss Yvonne Gleichner",
      "id": "PAT-79765"
    },
    "serviceDate": "04/04/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$13174.90",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "6:08 PM"
    },
    "user": "GM",
    "dateSent": "07/27/2025",
    "dateSentOrig": "07/27/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Kreiger Healthcare",
      "id": "PROV-4467"
    }
  },
  {
    "patient": {
      "name": "Larry Bailey",
      "id": "PAT-37247"
    },
    "serviceDate": "05/25/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$3413.78",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "8:07 AM"
    },
    "user": "KS",
    "dateSent": "09/02/2025",
    "dateSentOrig": "06/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Ernser Medical Group",
      "id": "PROV-8725"
    }
  },
  {
    "patient": {
      "name": "Lucy Bechtelar",
      "id": "PAT-76710"
    },
    "serviceDate": "08/06/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$8193.08",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "10:50 PM"
    },
    "user": "DB",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Flatley Healthcare",
      "id": "PROV-1933"
    }
  },
  {
    "patient": {
      "name": "Garry Rath",
      "id": "PAT-79930"
    },
    "serviceDate": "08/09/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$14234.84",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "2:34 PM"
    },
    "user": "KGD",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Bashirian Family Practice",
      "id": "PROV-8660"
    }
  },
  {
    "patient": {
      "name": "Sue Leannon",
      "id": "PAT-31390"
    },
    "serviceDate": "07/09/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$11825.26",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "8:28 AM"
    },
    "user": "KH",
    "dateSent": "08/11/2025",
    "dateSentOrig": "08/11/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Parisian Healthcare",
      "id": "PROV-6821"
    }
  },
  {
    "patient": {
      "name": "Gary Emmerich",
      "id": "PAT-25695"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$10944.52",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "6:21 AM"
    },
    "user": "SB",
    "dateSent": "09/03/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Simonis Medical Group",
      "id": "PROV-6450"
    }
  },
  {
    "patient": {
      "name": "Angelica Quigley",
      "id": "PAT-95898"
    },
    "serviceDate": "08/03/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$5637.11",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "8:19 PM"
    },
    "user": "PW",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Auer Associates",
      "id": "PROV-1015"
    }
  },
  {
    "patient": {
      "name": "Darin Swaniawski IV",
      "id": "PAT-73052"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$11873.31",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "11:00 PM"
    },
    "user": "MB",
    "dateSent": "05/11/2025",
    "dateSentOrig": "04/08/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Fahey Healthcare",
      "id": "PROV-7402"
    }
  },
  {
    "patient": {
      "name": "Suzanne Nikolaus",
      "id": "PAT-63211"
    },
    "serviceDate": "07/21/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$3743.23",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "12:46 AM"
    },
    "user": "MK",
    "dateSent": "07/25/2025",
    "dateSentOrig": "07/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Bergstrom Medical Group",
      "id": "PROV-6612"
    }
  },
  {
    "patient": {
      "name": "Perry Block-Konopelski",
      "id": "PAT-14881"
    },
    "serviceDate": "05/31/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$8465.09",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "11:49 AM"
    },
    "user": "KY",
    "dateSent": "08/18/2025",
    "dateSentOrig": "08/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Torp Medical Group",
      "id": "PROV-3842"
    }
  },
  {
    "patient": {
      "name": "Terrence Nitzsche",
      "id": "PAT-10431"
    },
    "serviceDate": "08/06/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$4549.50",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "6:33 AM"
    },
    "user": "TH",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Walsh Healthcare",
      "id": "PROV-2453"
    }
  },
  {
    "patient": {
      "name": "Terry Lakin",
      "id": "PAT-39876"
    },
    "serviceDate": "07/19/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$9032.17",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "3:09 PM"
    },
    "user": "SK",
    "dateSent": "08/06/2025",
    "dateSentOrig": "08/06/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Veum Healthcare",
      "id": "PROV-4697"
    }
  },
  {
    "patient": {
      "name": "Dr. Dominick Wilderman",
      "id": "PAT-27211"
    },
    "serviceDate": "07/18/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$13548.67",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "3:00 AM"
    },
    "user": "CBW",
    "dateSent": "08/13/2025",
    "dateSentOrig": "08/13/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Wiegand Healthcare",
      "id": "PROV-6876"
    }
  },
  {
    "patient": {
      "name": "Hugo McGlynn",
      "id": "PAT-53068"
    },
    "serviceDate": "04/04/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$11352.47",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "2:08 PM"
    },
    "user": "DB",
    "dateSent": "06/26/2025",
    "dateSentOrig": "05/25/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Zieme Medical Group",
      "id": "PROV-8198"
    }
  },
  {
    "patient": {
      "name": "Joshua Oberbrunner",
      "id": "PAT-31869"
    },
    "serviceDate": "07/06/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$4496.95",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "5:17 AM"
    },
    "user": "DAL",
    "dateSent": "08/11/2025",
    "dateSentOrig": "08/11/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Considine Healthcare",
      "id": "PROV-4971"
    }
  },
  {
    "patient": {
      "name": "Jeffery Stroman",
      "id": "PAT-90996"
    },
    "serviceDate": "07/13/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$7892.09",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "6:33 AM"
    },
    "user": "KP",
    "dateSent": "07/18/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Huels Family Practice",
      "id": "PROV-2166"
    }
  },
  {
    "patient": {
      "name": "Janice MacGyver Sr.",
      "id": "PAT-17282"
    },
    "serviceDate": "04/10/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$59.49",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "6:29 AM"
    },
    "user": "JAC",
    "dateSent": "05/26/2025",
    "dateSentOrig": "04/30/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Gleichner Associates",
      "id": "PROV-4065"
    }
  },
  {
    "patient": {
      "name": "Wendy Gulgowski",
      "id": "PAT-59277"
    },
    "serviceDate": "07/20/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Secondary"
    },
    "amount": "$13455.90",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "3:21 AM"
    },
    "user": "MRO",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/08/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Kovacek Medical Group",
      "id": "PROV-5524"
    }
  },
  {
    "patient": {
      "name": "Brandi Murazik",
      "id": "PAT-70843"
    },
    "serviceDate": "08/08/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$4925.71",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "12:05 AM"
    },
    "user": "EM",
    "dateSent": "08/26/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Hauck Clinic",
      "id": "PROV-7672"
    }
  },
  {
    "patient": {
      "name": "Marcella Prosacco",
      "id": "PAT-89839"
    },
    "serviceDate": "08/19/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$13994.65",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "1:27 AM"
    },
    "user": "SRW",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Erdman Clinic",
      "id": "PROV-9538"
    }
  },
  {
    "patient": {
      "name": "Eloise Keebler",
      "id": "PAT-71899"
    },
    "serviceDate": "05/17/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$4432.69",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "6:12 AM"
    },
    "user": "HG",
    "dateSent": "06/30/2025",
    "dateSentOrig": "06/30/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Douglas Associates",
      "id": "PROV-1674"
    }
  },
  {
    "patient": {
      "name": "Benjamin Littel Jr.",
      "id": "PAT-69040"
    },
    "serviceDate": "04/11/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$5661.22",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "4:06 PM"
    },
    "user": "AC",
    "dateSent": "06/15/2025",
    "dateSentOrig": "06/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Green Family Practice",
      "id": "PROV-4299"
    }
  },
  {
    "patient": {
      "name": "Ricardo Weimann",
      "id": "PAT-74725"
    },
    "serviceDate": "07/14/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$327.58",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "3:28 AM"
    },
    "user": "WM",
    "dateSent": "07/24/2025",
    "dateSentOrig": "07/23/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Kris Healthcare",
      "id": "PROV-1009"
    }
  },
  {
    "patient": {
      "name": "Charlotte Mante",
      "id": "PAT-38615"
    },
    "serviceDate": "04/16/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$11744.30",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "12:15 PM"
    },
    "user": "HR",
    "dateSent": "07/10/2025",
    "dateSentOrig": "07/10/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Mante Associates",
      "id": "PROV-3915"
    }
  },
  {
    "patient": {
      "name": "Mathew Dickens",
      "id": "PAT-12001"
    },
    "serviceDate": "07/02/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$2247.74",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "10:51 AM"
    },
    "user": "LAO",
    "dateSent": "08/02/2025",
    "dateSentOrig": "08/02/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Mitchell Healthcare",
      "id": "PROV-6655"
    }
  },
  {
    "patient": {
      "name": "Leland Jaskolski MD",
      "id": "PAT-12978"
    },
    "serviceDate": "05/17/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$12621.48",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "8:26 AM"
    },
    "user": "FBJ",
    "dateSent": "06/04/2025",
    "dateSentOrig": "05/18/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Pollich Healthcare",
      "id": "PROV-1752"
    }
  },
  {
    "patient": {
      "name": "Darin Hudson",
      "id": "PAT-57177"
    },
    "serviceDate": "03/18/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$11858.14",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "10:47 AM"
    },
    "user": "RD",
    "dateSent": "05/31/2025",
    "dateSentOrig": "04/16/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Wolf Associates",
      "id": "PROV-5795"
    }
  },
  {
    "patient": {
      "name": "Lucia Emmerich",
      "id": "PAT-35247"
    },
    "serviceDate": "04/06/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$7801.88",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "4:47 AM"
    },
    "user": "KLL",
    "dateSent": "06/22/2025",
    "dateSentOrig": "06/22/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Rodriguez Medical Group",
      "id": "PROV-6163"
    }
  },
  {
    "patient": {
      "name": "Jessie Schroeder-Franecki",
      "id": "PAT-99973"
    },
    "serviceDate": "07/19/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$10332.11",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "2:25 PM"
    },
    "user": "JR",
    "dateSent": "09/01/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hahn Family Practice",
      "id": "PROV-5445"
    }
  },
  {
    "patient": {
      "name": "Cristina Shanahan",
      "id": "PAT-62713"
    },
    "serviceDate": "06/19/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$8985.58",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/19/2025",
      "time": "12:08 PM"
    },
    "user": "AHH",
    "dateSent": "07/19/2025",
    "dateSentOrig": "07/19/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Jacobi Healthcare",
      "id": "PROV-4730"
    }
  },
  {
    "patient": {
      "name": "Teri Gleason",
      "id": "PAT-57048"
    },
    "serviceDate": "05/04/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$2561.34",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "11:14 AM"
    },
    "user": "FT",
    "dateSent": "07/01/2025",
    "dateSentOrig": "05/28/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Fritsch Associates",
      "id": "PROV-4244"
    }
  },
  {
    "patient": {
      "name": "Neal Bahringer",
      "id": "PAT-45870"
    },
    "serviceDate": "03/12/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$13567.28",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "9:47 AM"
    },
    "user": "TSM",
    "dateSent": "08/16/2025",
    "dateSentOrig": "05/21/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "DuBuque Healthcare",
      "id": "PROV-3947"
    }
  },
  {
    "patient": {
      "name": "Ignacio Hane PhD",
      "id": "PAT-15303"
    },
    "serviceDate": "04/03/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$4780.26",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "9:17 AM"
    },
    "user": "JW",
    "dateSent": "07/30/2025",
    "dateSentOrig": "05/17/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Purdy Healthcare",
      "id": "PROV-8693"
    }
  },
  {
    "patient": {
      "name": "Bruce Predovic",
      "id": "PAT-97216"
    },
    "serviceDate": "04/21/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$9145.57",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "11:35 PM"
    },
    "user": "EF",
    "dateSent": "06/13/2025",
    "dateSentOrig": "06/13/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Kunze Associates",
      "id": "PROV-5871"
    }
  },
  {
    "patient": {
      "name": "Irma Trantow",
      "id": "PAT-69291"
    },
    "serviceDate": "08/31/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$10960.65",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "2:13 AM"
    },
    "user": "DB",
    "dateSent": "09/01/2025",
    "dateSentOrig": "09/01/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Skiles Family Practice",
      "id": "PROV-3964"
    }
  },
  {
    "patient": {
      "name": "Lynn Erdman",
      "id": "PAT-28330"
    },
    "serviceDate": "05/10/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$2519.07",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "8:42 PM"
    },
    "user": "DS",
    "dateSent": "07/26/2025",
    "dateSentOrig": "07/26/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Prosacco Medical Group",
      "id": "PROV-4042"
    }
  },
  {
    "patient": {
      "name": "Lorenzo Larkin",
      "id": "PAT-19619"
    },
    "serviceDate": "04/18/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$1485.57",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "12:52 PM"
    },
    "user": "EO",
    "dateSent": "07/16/2025",
    "dateSentOrig": "07/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hackett Associates",
      "id": "PROV-6526"
    }
  },
  {
    "patient": {
      "name": "Jody Rippin",
      "id": "PAT-81393"
    },
    "serviceDate": "04/10/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$2135.25",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "4:13 PM"
    },
    "user": "RAB",
    "dateSent": "08/22/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Rosenbaum Associates",
      "id": "PROV-7728"
    }
  },
  {
    "patient": {
      "name": "Wilfred Lueilwitz",
      "id": "PAT-70508"
    },
    "serviceDate": "05/30/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$7933.75",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/26/2025",
      "time": "11:04 PM"
    },
    "user": "GLR",
    "dateSent": "07/12/2025",
    "dateSentOrig": "07/12/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Lind Family Practice",
      "id": "PROV-2311"
    }
  },
  {
    "patient": {
      "name": "James Ebert",
      "id": "PAT-69897"
    },
    "serviceDate": "05/18/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$737.14",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/21/2025",
      "time": "10:00 PM"
    },
    "user": "AED",
    "dateSent": "06/07/2025",
    "dateSentOrig": "06/07/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Hoppe-Krajcik Family Practice",
      "id": "PROV-9572"
    }
  },
  {
    "patient": {
      "name": "Terrance Mills",
      "id": "PAT-93661"
    },
    "serviceDate": "07/01/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$11956.05",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "8:26 AM"
    },
    "user": "LS",
    "dateSent": "07/06/2025",
    "dateSentOrig": "07/06/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Mitchell Associates",
      "id": "PROV-9732"
    }
  },
  {
    "patient": {
      "name": "Pamela Barton",
      "id": "PAT-76671"
    },
    "serviceDate": "04/23/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$228.14",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "6:10 AM"
    },
    "user": "GJM",
    "dateSent": "05/20/2025",
    "dateSentOrig": "05/20/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "McGlynn Family Practice",
      "id": "PROV-6710"
    }
  },
  {
    "patient": {
      "name": "Alma Littel",
      "id": "PAT-11872"
    },
    "serviceDate": "08/15/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$699.02",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "11:03 AM"
    },
    "user": "EB",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Gusikowski Associates",
      "id": "PROV-2284"
    }
  },
  {
    "patient": {
      "name": "Mona Hansen",
      "id": "PAT-21361"
    },
    "serviceDate": "07/11/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$14472.22",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "4:45 PM"
    },
    "user": "DLD",
    "dateSent": "08/28/2025",
    "dateSentOrig": "07/29/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hills Family Practice",
      "id": "PROV-7773"
    }
  },
  {
    "patient": {
      "name": "Mrs. Pearl Leffler",
      "id": "PAT-20979"
    },
    "serviceDate": "07/23/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$4136.36",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "1:46 PM"
    },
    "user": "AM",
    "dateSent": "07/24/2025",
    "dateSentOrig": "07/23/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Hessel Family Practice",
      "id": "PROV-9529"
    }
  },
  {
    "patient": {
      "name": "Dr. Yvonne Lemke",
      "id": "PAT-17896"
    },
    "serviceDate": "07/11/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$13791.96",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "9:32 PM"
    },
    "user": "CNW",
    "dateSent": "09/03/2025",
    "dateSentOrig": "09/03/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Gusikowski Healthcare",
      "id": "PROV-7241"
    }
  },
  {
    "patient": {
      "name": "Cheryl Wisoky",
      "id": "PAT-87373"
    },
    "serviceDate": "04/22/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$5973.68",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "5:09 AM"
    },
    "user": "ZQK",
    "dateSent": "05/11/2025",
    "dateSentOrig": "05/11/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Konopelski Clinic",
      "id": "PROV-7307"
    }
  },
  {
    "patient": {
      "name": "Nathan Schowalter V",
      "id": "PAT-72898"
    },
    "serviceDate": "05/25/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$3333.20",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "5:51 PM"
    },
    "user": "JMM",
    "dateSent": "05/26/2025",
    "dateSentOrig": "05/26/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Orn Medical Group",
      "id": "PROV-5909"
    }
  },
  {
    "patient": {
      "name": "Dianna McClure",
      "id": "PAT-10615"
    },
    "serviceDate": "06/19/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$2911.61",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "11:20 AM"
    },
    "user": "KM",
    "dateSent": "07/18/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Heller Healthcare",
      "id": "PROV-2648"
    }
  },
  {
    "patient": {
      "name": "Patricia Kling",
      "id": "PAT-57012"
    },
    "serviceDate": "08/05/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$14221.80",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "4:39 AM"
    },
    "user": "GA",
    "dateSent": "08/12/2025",
    "dateSentOrig": "08/12/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Connelly Medical Group",
      "id": "PROV-8040"
    }
  },
  {
    "patient": {
      "name": "Eva Miller",
      "id": "PAT-91089"
    },
    "serviceDate": "08/18/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$12913.96",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "2:52 AM"
    },
    "user": "JQR",
    "dateSent": "08/22/2025",
    "dateSentOrig": "08/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Steuber Associates",
      "id": "PROV-8176"
    }
  },
  {
    "patient": {
      "name": "Rafael Miller",
      "id": "PAT-73417"
    },
    "serviceDate": "03/10/2025",
    "insuranceCarrier": {
      "carrierName": "Kaiser Permanente",
      "planCategory": "Primary"
    },
    "amount": "$6264.53",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "1:34 AM"
    },
    "user": "TJM",
    "dateSent": "03/19/2025",
    "dateSentOrig": "03/19/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Wilderman Healthcare",
      "id": "PROV-8048"
    }
  },
  {
    "patient": {
      "name": "Boyd Paucek",
      "id": "PAT-52216"
    },
    "serviceDate": "05/28/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Secondary"
    },
    "amount": "$9692.64",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "9:59 AM"
    },
    "user": "JHS",
    "dateSent": "07/23/2025",
    "dateSentOrig": "06/06/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Marvin Associates",
      "id": "PROV-9466"
    }
  },
  {
    "patient": {
      "name": "Ms. Jeannie Jacobi",
      "id": "PAT-67588"
    },
    "serviceDate": "03/22/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$14012.34",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "7:50 AM"
    },
    "user": "BJ",
    "dateSent": "04/28/2025",
    "dateSentOrig": "04/17/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Fisher Clinic",
      "id": "PROV-6841"
    }
  },
  {
    "patient": {
      "name": "Dr. Brandy Kris",
      "id": "PAT-79822"
    },
    "serviceDate": "09/04/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$14633.48",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "10:22 PM"
    },
    "user": "RK",
    "dateSent": "09/04/2025",
    "dateSentOrig": "09/04/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Heller Healthcare",
      "id": "PROV-8642"
    }
  },
  {
    "patient": {
      "name": "Mark Buckridge",
      "id": "PAT-66512"
    },
    "serviceDate": "04/24/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$1410.99",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "3:32 PM"
    },
    "user": "CM",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Nitzsche Family Practice",
      "id": "PROV-9554"
    }
  },
  {
    "patient": {
      "name": "Crystal Ernser",
      "id": "PAT-89223"
    },
    "serviceDate": "08/05/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$9428.64",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "1:01 AM"
    },
    "user": "LW",
    "dateSent": "08/29/2025",
    "dateSentOrig": "08/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Runte Medical Group",
      "id": "PROV-5846"
    }
  },
  {
    "patient": {
      "name": "Casey Pfannerstill",
      "id": "PAT-34017"
    },
    "serviceDate": "03/10/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$1591.38",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/08/2025",
      "time": "6:56 AM"
    },
    "user": "DK",
    "dateSent": "06/13/2025",
    "dateSentOrig": "04/20/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Botsford Medical Group",
      "id": "PROV-6821"
    }
  },
  {
    "patient": {
      "name": "Delbert Hartmann",
      "id": "PAT-88989"
    },
    "serviceDate": "04/23/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$5074.15",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "10:52 PM"
    },
    "user": "OH",
    "dateSent": "08/19/2025",
    "dateSentOrig": "07/28/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Hessel Healthcare",
      "id": "PROV-9582"
    }
  },
  {
    "patient": {
      "name": "Maria Medhurst",
      "id": "PAT-25480"
    },
    "serviceDate": "05/25/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$1723.02",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "10:28 PM"
    },
    "user": "VB",
    "dateSent": "06/28/2025",
    "dateSentOrig": "06/28/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Gerhold Clinic",
      "id": "PROV-6326"
    }
  },
  {
    "patient": {
      "name": "Garry Jacobs II",
      "id": "PAT-75684"
    },
    "serviceDate": "06/14/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$5253.19",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "7:01 PM"
    },
    "user": "MK",
    "dateSent": "07/10/2025",
    "dateSentOrig": "07/10/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Ledner Medical Group",
      "id": "PROV-3066"
    }
  },
  {
    "patient": {
      "name": "Janis Moore",
      "id": "PAT-10606"
    },
    "serviceDate": "04/12/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$654.93",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/11/2025",
      "time": "6:14 PM"
    },
    "user": "ABK",
    "dateSent": "07/04/2025",
    "dateSentOrig": "07/04/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Lockman-Walter Family Practice",
      "id": "PROV-1025"
    }
  },
  {
    "patient": {
      "name": "Angel Brekke",
      "id": "PAT-55868"
    },
    "serviceDate": "08/22/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$583.80",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "3:25 PM"
    },
    "user": "GEW",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kuhic Family Practice",
      "id": "PROV-6247"
    }
  },
  {
    "patient": {
      "name": "Kristi Larson",
      "id": "PAT-75716"
    },
    "serviceDate": "04/07/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$3222.95",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "5:35 PM"
    },
    "user": "KS",
    "dateSent": "05/17/2025",
    "dateSentOrig": "04/19/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Bogisich Healthcare",
      "id": "PROV-8277"
    }
  },
  {
    "patient": {
      "name": "Christopher Kling",
      "id": "PAT-45492"
    },
    "serviceDate": "08/24/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$12514.80",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "5:59 PM"
    },
    "user": "HSK",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Bauch Healthcare",
      "id": "PROV-5943"
    }
  },
  {
    "patient": {
      "name": "Abraham Rutherford",
      "id": "PAT-47346"
    },
    "serviceDate": "04/05/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$732.93",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "10:55 AM"
    },
    "user": "MSB",
    "dateSent": "06/27/2025",
    "dateSentOrig": "06/27/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "O'Hara Medical Group",
      "id": "PROV-2523"
    }
  },
  {
    "patient": {
      "name": "Kelli Monahan",
      "id": "PAT-55392"
    },
    "serviceDate": "04/08/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$734.45",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "5:33 AM"
    },
    "user": "PC",
    "dateSent": "08/03/2025",
    "dateSentOrig": "05/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Miller Associates",
      "id": "PROV-6722"
    }
  },
  {
    "patient": {
      "name": "Rodney Lehner",
      "id": "PAT-76546"
    },
    "serviceDate": "04/07/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$3207.60",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "7:38 AM"
    },
    "user": "RB",
    "dateSent": "08/31/2025",
    "dateSentOrig": "08/31/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Collier-Bruen Family Practice",
      "id": "PROV-8390"
    }
  },
  {
    "patient": {
      "name": "Renee Rolfson DVM",
      "id": "PAT-90136"
    },
    "serviceDate": "07/03/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$10967.68",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "6:40 AM"
    },
    "user": "BK",
    "dateSent": "07/29/2025",
    "dateSentOrig": "07/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Hodkiewicz Medical Group",
      "id": "PROV-4753"
    }
  },
  {
    "patient": {
      "name": "Gretchen Dicki",
      "id": "PAT-21069"
    },
    "serviceDate": "07/17/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Secondary"
    },
    "amount": "$1461.63",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/03/2025",
      "time": "8:35 PM"
    },
    "user": "EH",
    "dateSent": "08/24/2025",
    "dateSentOrig": "08/24/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Jakubowski-Durgan Medical Group",
      "id": "PROV-7850"
    }
  },
  {
    "patient": {
      "name": "Lee Hagenes",
      "id": "PAT-79731"
    },
    "serviceDate": "04/23/2025",
    "insuranceCarrier": {
      "carrierName": "Aetna",
      "planCategory": "Primary"
    },
    "amount": "$9101.47",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "6:28 AM"
    },
    "user": "DW",
    "dateSent": "08/10/2025",
    "dateSentOrig": "08/10/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Willms Family Practice",
      "id": "PROV-8365"
    }
  },
  {
    "patient": {
      "name": "Erin Kris",
      "id": "PAT-90991"
    },
    "serviceDate": "06/02/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$3336.19",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "6:14 PM"
    },
    "user": "JP",
    "dateSent": "08/16/2025",
    "dateSentOrig": "06/03/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Wolf Clinic",
      "id": "PROV-2547"
    }
  },
  {
    "patient": {
      "name": "Rolando Weber",
      "id": "PAT-98662"
    },
    "serviceDate": "09/02/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$4640.53",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "2:59 AM"
    },
    "user": "FF",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Waters Associates",
      "id": "PROV-6437"
    }
  },
  {
    "patient": {
      "name": "Pedro Quitzon",
      "id": "PAT-46864"
    },
    "serviceDate": "03/15/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Primary"
    },
    "amount": "$7924.36",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/24/2025",
      "time": "11:04 AM"
    },
    "user": "LY",
    "dateSent": "08/17/2025",
    "dateSentOrig": "07/04/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Morar Family Practice",
      "id": "PROV-3048"
    }
  },
  {
    "patient": {
      "name": "Elena Tremblay-Bechtelar",
      "id": "PAT-22788"
    },
    "serviceDate": "03/20/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$2733.82",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "5:20 AM"
    },
    "user": "DSM",
    "dateSent": "08/05/2025",
    "dateSentOrig": "05/15/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Crona Clinic",
      "id": "PROV-1361"
    }
  },
  {
    "patient": {
      "name": "Shaun Gutmann",
      "id": "PAT-26442"
    },
    "serviceDate": "03/22/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$8591.38",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "12:57 AM"
    },
    "user": "GF",
    "dateSent": "08/28/2025",
    "dateSentOrig": "08/28/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Beier-Sanford Associates",
      "id": "PROV-6328"
    }
  },
  {
    "patient": {
      "name": "Dr. Salvador Collier",
      "id": "PAT-39610"
    },
    "serviceDate": "08/13/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$3992.52",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "2:47 AM"
    },
    "user": "ACE",
    "dateSent": "09/02/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Mohr Healthcare",
      "id": "PROV-1665"
    }
  },
  {
    "patient": {
      "name": "Jonathon Wyman",
      "id": "PAT-32304"
    },
    "serviceDate": "07/10/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$104.71",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "5:55 PM"
    },
    "user": "SW",
    "dateSent": "07/16/2025",
    "dateSentOrig": "07/11/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Feest Clinic",
      "id": "PROV-9509"
    }
  },
  {
    "patient": {
      "name": "Susan Bergnaum",
      "id": "PAT-59134"
    },
    "serviceDate": "05/06/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$1522.53",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/31/2025",
      "time": "12:25 AM"
    },
    "user": "ABN",
    "dateSent": "07/11/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Corkery-Franey Family Practice",
      "id": "PROV-2475"
    }
  },
  {
    "patient": {
      "name": "Pam Gutkowski",
      "id": "PAT-34313"
    },
    "serviceDate": "07/01/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$11203.46",
    "status": "PENDING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "10:51 AM"
    },
    "user": "HBV",
    "dateSent": "07/22/2025",
    "dateSentOrig": "07/22/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Metz Medical Group",
      "id": "PROV-4310"
    }
  },
  {
    "patient": {
      "name": "Spencer Hermiston",
      "id": "PAT-11760"
    },
    "serviceDate": "07/05/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$7360.29",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/14/2025",
      "time": "8:45 PM"
    },
    "user": "TEK",
    "dateSent": "08/22/2025",
    "dateSentOrig": "07/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Parisian Family Practice",
      "id": "PROV-6433"
    }
  },
  {
    "patient": {
      "name": "Jan Auer",
      "id": "PAT-64744"
    },
    "serviceDate": "03/21/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$5206.20",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/17/2025",
      "time": "12:11 AM"
    },
    "user": "BAU",
    "dateSent": "07/02/2025",
    "dateSentOrig": "03/27/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Invalid patient ID",
      "isSynced": false
    },
    "provider": {
      "name": "Bashirian Clinic",
      "id": "PROV-6000"
    }
  },
  {
    "patient": {
      "name": "Santiago Schuppe",
      "id": "PAT-30018"
    },
    "serviceDate": "06/19/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$1737.39",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/06/2025",
      "time": "10:11 PM"
    },
    "user": "NM",
    "dateSent": "07/03/2025",
    "dateSentOrig": "06/24/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Deckow Associates",
      "id": "PROV-3064"
    }
  },
  {
    "patient": {
      "name": "Edith Connelly",
      "id": "PAT-97911"
    },
    "serviceDate": "05/02/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$13905.45",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/18/2025",
      "time": "6:06 AM"
    },
    "user": "FO",
    "dateSent": "05/27/2025",
    "dateSentOrig": "05/27/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Ortiz Associates",
      "id": "PROV-1082"
    }
  },
  {
    "patient": {
      "name": "Mrs. Olive Cormier MD",
      "id": "PAT-77041"
    },
    "serviceDate": "07/26/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Primary"
    },
    "amount": "$9377.60",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "8:23 AM"
    },
    "user": "DB",
    "dateSent": "07/28/2025",
    "dateSentOrig": "07/26/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Hintz Associates",
      "id": "PROV-4344"
    }
  },
  {
    "patient": {
      "name": "Candace Bode",
      "id": "PAT-13761"
    },
    "serviceDate": "04/19/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Secondary"
    },
    "amount": "$12370.18",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "8:20 AM"
    },
    "user": "MLL",
    "dateSent": "07/18/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Ondricka Family Practice",
      "id": "PROV-6603"
    }
  },
  {
    "patient": {
      "name": "Dr. Edmund Lowe",
      "id": "PAT-53827"
    },
    "serviceDate": "08/07/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$14708.26",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "1:08 PM"
    },
    "user": "ASR",
    "dateSent": "08/09/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Willms Healthcare",
      "id": "PROV-2300"
    }
  },
  {
    "patient": {
      "name": "Heather Kozey",
      "id": "PAT-58730"
    },
    "serviceDate": "05/07/2025",
    "insuranceCarrier": {
      "carrierName": "Centene",
      "planCategory": "Primary"
    },
    "amount": "$9824.24",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "4:59 AM"
    },
    "user": "MTR",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Stiedemann Family Practice",
      "id": "PROV-7476"
    }
  },
  {
    "patient": {
      "name": "Alfredo Rodriguez",
      "id": "PAT-89844"
    },
    "serviceDate": "04/23/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$12518.95",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/16/2025",
      "time": "5:50 PM"
    },
    "user": "TK",
    "dateSent": "08/30/2025",
    "dateSentOrig": "08/30/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Reichel Associates",
      "id": "PROV-1470"
    }
  },
  {
    "patient": {
      "name": "Dr. Ignacio Kub",
      "id": "PAT-23122"
    },
    "serviceDate": "05/15/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$5216.10",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/15/2025",
      "time": "6:56 PM"
    },
    "user": "AB",
    "dateSent": "07/05/2025",
    "dateSentOrig": "07/05/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Mann Associates",
      "id": "PROV-7474"
    }
  },
  {
    "patient": {
      "name": "Eva Oberbrunner",
      "id": "PAT-21867"
    },
    "serviceDate": "08/24/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$9724.55",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/27/2025",
      "time": "12:19 AM"
    },
    "user": "EDL",
    "dateSent": "09/01/2025",
    "dateSentOrig": "09/01/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Klocko Family Practice",
      "id": "PROV-9614"
    }
  },
  {
    "patient": {
      "name": "Miss Deborah Murray IV",
      "id": "PAT-86208"
    },
    "serviceDate": "07/14/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$5882.68",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "1:08 PM"
    },
    "user": "AT",
    "dateSent": "07/18/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Prosacco Medical Group",
      "id": "PROV-4366"
    }
  },
  {
    "patient": {
      "name": "Christina Treutel",
      "id": "PAT-54874"
    },
    "serviceDate": "05/31/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$8999.36",
    "status": "DENIED",
    "lastUpdated": {
      "date": "09/01/2025",
      "time": "6:46 PM"
    },
    "user": "IR",
    "dateSent": "08/04/2025",
    "dateSentOrig": "06/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Quigley Healthcare",
      "id": "PROV-5970"
    }
  },
  {
    "patient": {
      "name": "Stacy Reichert",
      "id": "PAT-81264"
    },
    "serviceDate": "08/06/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$11041.29",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/22/2025",
      "time": "9:04 AM"
    },
    "user": "MK",
    "dateSent": "08/10/2025",
    "dateSentOrig": "08/09/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Kutch Medical Group",
      "id": "PROV-5686"
    }
  },
  {
    "patient": {
      "name": "Jennifer Cassin",
      "id": "PAT-89613"
    },
    "serviceDate": "04/13/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Primary"
    },
    "amount": "$12809.43",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "8:49 PM"
    },
    "user": "HR",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Missing required fields",
      "isSynced": false
    },
    "provider": {
      "name": "Yost Healthcare",
      "id": "PROV-4810"
    }
  },
  {
    "patient": {
      "name": "Miss Kathy Maggio",
      "id": "PAT-90545"
    },
    "serviceDate": "06/08/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$7748.96",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/12/2025",
      "time": "10:16 PM"
    },
    "user": "HC",
    "dateSent": "06/22/2025",
    "dateSentOrig": "06/13/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Ryan Clinic",
      "id": "PROV-4266"
    }
  },
  {
    "patient": {
      "name": "Doreen Marks III",
      "id": "PAT-23320"
    },
    "serviceDate": "03/29/2025",
    "insuranceCarrier": {
      "carrierName": "Blue Cross Blue Shield",
      "planCategory": "Primary"
    },
    "amount": "$1630.34",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/20/2025",
      "time": "3:22 AM"
    },
    "user": "MSV",
    "dateSent": "03/29/2025",
    "dateSentOrig": "03/29/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Wunsch Medical Group",
      "id": "PROV-1220"
    }
  },
  {
    "patient": {
      "name": "Dr. Victor McDermott",
      "id": "PAT-66275"
    },
    "serviceDate": "03/13/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Primary"
    },
    "amount": "$3577.27",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "6:06 AM"
    },
    "user": "BJ",
    "dateSent": "08/09/2025",
    "dateSentOrig": "07/04/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Waiting for authorization",
      "isSynced": false
    },
    "provider": {
      "name": "Renner Associates",
      "id": "PROV-6739"
    }
  },
  {
    "patient": {
      "name": "Derek Langworth",
      "id": "PAT-99419"
    },
    "serviceDate": "08/05/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$3000.59",
    "status": "PENDING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "10:25 AM"
    },
    "user": "MBB",
    "dateSent": "08/16/2025",
    "dateSentOrig": "08/16/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Cole Associates",
      "id": "PROV-8365"
    }
  },
  {
    "patient": {
      "name": "Johnathan Kautzer DDS",
      "id": "PAT-56843"
    },
    "serviceDate": "06/07/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Secondary"
    },
    "amount": "$4085.22",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "09/02/2025",
      "time": "4:46 PM"
    },
    "user": "TAL",
    "dateSent": "07/31/2025",
    "dateSentOrig": "07/31/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Veum Medical Group",
      "id": "PROV-4033"
    }
  },
  {
    "patient": {
      "name": "Mr. Isaac Bartoletti",
      "id": "PAT-63909"
    },
    "serviceDate": "03/27/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$9646.13",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/10/2025",
      "time": "1:48 PM"
    },
    "user": "EB",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Walter Associates",
      "id": "PROV-2113"
    }
  },
  {
    "patient": {
      "name": "Sally Lueilwitz",
      "id": "PAT-92037"
    },
    "serviceDate": "08/23/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Secondary"
    },
    "amount": "$578.69",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/13/2025",
      "time": "9:02 AM"
    },
    "user": "DM",
    "dateSent": "08/25/2025",
    "dateSentOrig": "08/25/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Connection timeout",
      "isSynced": false
    },
    "provider": {
      "name": "Willms Medical Group",
      "id": "PROV-9446"
    }
  },
  {
    "patient": {
      "name": "Stephanie Olson",
      "id": "PAT-19083"
    },
    "serviceDate": "04/21/2025",
    "insuranceCarrier": {
      "carrierName": "Cigna",
      "planCategory": "Secondary"
    },
    "amount": "$5680.03",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "2:27 PM"
    },
    "user": "JAL",
    "dateSent": "08/09/2025",
    "dateSentOrig": "07/18/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Simonis Healthcare",
      "id": "PROV-4142"
    }
  },
  {
    "patient": {
      "name": "Dr. Saul Streich",
      "id": "PAT-11643"
    },
    "serviceDate": "04/10/2025",
    "insuranceCarrier": {
      "carrierName": "Humana",
      "planCategory": "Secondary"
    },
    "amount": "$11061.25",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/25/2025",
      "time": "2:31 PM"
    },
    "user": "OG",
    "dateSent": "06/10/2025",
    "dateSentOrig": "06/10/2025",
    "pmsSyncStatus": {
      "status": "Synced",
      "description": "Successfully synced to PMS",
      "isSynced": true
    },
    "provider": {
      "name": "Baumbach Medical Group",
      "id": "PROV-5729"
    }
  },
  {
    "patient": {
      "name": "Sheri Tremblay",
      "id": "PAT-26450"
    },
    "serviceDate": "06/18/2025",
    "insuranceCarrier": {
      "carrierName": "UnitedHealthcare",
      "planCategory": "Secondary"
    },
    "amount": "$12120.74",
    "status": "DENIED",
    "lastUpdated": {
      "date": "08/23/2025",
      "time": "10:06 AM"
    },
    "user": "SPG",
    "dateSent": "08/17/2025",
    "dateSentOrig": "08/17/2025",
    "pmsSyncStatus": {
      "status": "Syncing",
      "description": "Currently syncing...",
      "isSynced": false
    },
    "provider": {
      "name": "Adams Family Practice",
      "id": "PROV-6419"
    }
  },
  {
    "patient": {
      "name": "Regina Heathcote-Brakus",
      "id": "PAT-68684"
    },
    "serviceDate": "05/22/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$2247.43",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/28/2025",
      "time": "1:23 PM"
    },
    "user": "MJD",
    "dateSent": "06/04/2025",
    "dateSentOrig": "05/25/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "System maintenance",
      "isSynced": false
    },
    "provider": {
      "name": "Ebert Associates",
      "id": "PROV-3631"
    }
  },
  {
    "patient": {
      "name": "Randolph Kuphal",
      "id": "PAT-71604"
    },
    "serviceDate": "07/07/2025",
    "insuranceCarrier": {
      "carrierName": "Molina Healthcare",
      "planCategory": "Primary"
    },
    "amount": "$1619.98",
    "status": "PROCESSING",
    "lastUpdated": {
      "date": "08/30/2025",
      "time": "4:18 PM"
    },
    "user": "JM",
    "dateSent": "08/23/2025",
    "dateSentOrig": "07/09/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Ernser Healthcare",
      "id": "PROV-7844"
    }
  },
  {
    "patient": {
      "name": "Mindy Hegmann IV",
      "id": "PAT-29119"
    },
    "serviceDate": "05/20/2025",
    "insuranceCarrier": {
      "carrierName": "Independence Blue Cross",
      "planCategory": "Secondary"
    },
    "amount": "$10740.10",
    "status": "PAID",
    "lastUpdated": {
      "date": "08/29/2025",
      "time": "5:00 PM"
    },
    "user": "KP",
    "dateSent": "07/13/2025",
    "dateSentOrig": "05/29/2025",
    "pmsSyncStatus": {
      "status": "Error",
      "description": "Authentication failed",
      "isSynced": false
    },
    "provider": {
      "name": "Deckow Associates",
      "id": "PROV-5904"
    }
  },
  {
    "patient": {
      "name": "Tracey Donnelly",
      "id": "PAT-30119"
    },
    "serviceDate": "07/30/2025",
    "insuranceCarrier": {
      "carrierName": "Anthem",
      "planCategory": "Primary"
    },
    "amount": "$10291.84",
    "status": "NCOF - RESUBMITTED",
    "lastUpdated": {
      "date": "08/09/2025",
      "time": "10:41 AM"
    },
    "user": "TT",
    "dateSent": "09/02/2025",
    "dateSentOrig": "09/02/2025",
    "pmsSyncStatus": {
      "status": "Not synced",
      "description": "Pending manual review",
      "isSynced": false
    },
    "provider": {
      "name": "Breitenberg Associates",
      "id": "PROV-3232"
    }
  }
];

export default insuranceClaimsData;

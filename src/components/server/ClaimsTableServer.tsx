import { TableColumn } from "../TableHeader";
import { InsuranceClaimsResponse } from "../../lib/insurance-service";
import { ParsedClaimsParams } from "../../types/search-params";
import ClaimsDataDisplay from "./ClaimsDataDisplay";
import ClaimsFilters from "../client/ClaimsFilters";
import ClaimsPagination from "../client/ClaimsPagination";
import ClaimsTableHeader from "../client/ClaimsTableHeader";

interface ClaimsTableServerProps {
  columns: TableColumn[];
  claimsData: InsuranceClaimsResponse;
  searchParams: ParsedClaimsParams;
}

/**
 * Server component that renders the main table structure
 * Handles the layout and delegates interactive elements to client components
 */
export default function ClaimsTableServer({
  columns,
  claimsData,
  searchParams
}: ClaimsTableServerProps) {
  // Calculate grid template for consistent column widths
  const gridTemplate = columns.map(col => col.width).join(' ');
  const minWidth = columns.reduce((sum, col) => {
    const width = parseInt(col.width.replace('px', ''));
    return sum + width;
  }, 0) + (columns.length - 1) * 16; // Add gap spacing

  return (
    <div 
      className="w-full bg-white rounded-2xl shadow-lg overflow-hidden relative" 
      style={{ maxWidth: `${minWidth}px`, height: 'calc(100vh - 8rem)' }}
    >
      <div className="h-full flex flex-col">
        {/* Filters and Search - Client Component */}
        <div className="bg-gray-50 border-b border-gray-200 p-4">
          <ClaimsFilters
            currentFilters={{
              patientName: searchParams.patientName || '',
              status: searchParams.status || ''
            }}
          />
        </div>

        {/* Table Header - Server Component with Client Sorting */}
        <ClaimsTableHeader
          columns={columns}
          gridTemplate={gridTemplate}
          currentSorting={{
            column: searchParams.sortBy || null,
            direction: searchParams.sortDirection
          }}
        />

        {/* Data Display - Server Component */}
        <div className="flex-1 overflow-y-auto min-h-0">
          <ClaimsDataDisplay
            data={claimsData.data}
            columns={columns}
            startIndex={(searchParams.page - 1) * searchParams.limit}
          />
        </div>

        {/* Pagination - Client Component */}
        <div className="bg-gray-50 border-t border-gray-200 p-4">
          <ClaimsPagination 
            currentPage={claimsData.pagination.page}
            totalPages={claimsData.pagination.totalPages}
            totalItems={claimsData.pagination.total}
            itemsPerPage={claimsData.pagination.limit}
          />
        </div>
      </div>
    </div>
  );
}

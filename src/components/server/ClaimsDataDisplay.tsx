import { ClaimRowData } from "../ClaimRow";
import { TableColumn } from "../TableHeader";
import ClaimRow from "../ClaimRow";

interface ClaimsDataDisplayProps {
  data: ClaimRowData[];
  columns: TableColumn[];
  startIndex: number;
}

/**
 * Server component that renders the table data
 * This component is purely for display and contains no interactive elements
 */
export default function ClaimsDataDisplay({
  data,
  columns,
  startIndex
}: ClaimsDataDisplayProps) {
  // Show empty state if no data
  if (data.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-gray-500">
        <div className="text-6xl mb-4">📋</div>
        <h3 className="text-lg font-medium mb-2">No claims found</h3>
        <p className="text-sm text-center max-w-md">
          No insurance claims match your current filters. Try adjusting your search criteria or clearing the filters to see all claims.
        </p>
      </div>
    );
  }

  return (
    <div>
      {data.map((rowData, index) => (
        <div
          key={`${rowData.patient.id}-${startIndex + index}`}
          className="hover:bg-gray-50 transition-colors duration-150"
        >
          <ClaimRow
            data={rowData}
            columns={columns.map(col => ({ key: col.key, width: col.width }))}
          />
        </div>
      ))}
    </div>
  );
}

/**
 * Empty state component for when filters return no results
 */
export function EmptyFilterState({ 
  onClearFilters 
}: { 
  onClearFilters: () => void 
}) {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-gray-500">
      <div className="text-6xl mb-4">🔍</div>
      <h3 className="text-lg font-medium mb-2">No results found</h3>
      <p className="text-sm text-center max-w-md mb-4">
        Your current filters didn&apos;t match any claims. Try adjusting your search criteria.
      </p>
      <button
        onClick={onClearFilters}
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
      >
        Clear all filters
      </button>
    </div>
  );
}

'use client';

import { useState, useTransition } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { ClaimStatus } from '../ClaimRow';

interface ClaimsFiltersProps {
  currentFilters: {
    patientName: string;
    status: ClaimStatus | '';
  };
}

/**
 * Client component for search and filter inputs
 * Handles user interactions and URL updates
 */
export default function ClaimsFilters({ currentFilters }: ClaimsFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  
  // Local state for form inputs
  const [patientName, setPatientName] = useState(currentFilters.patientName);
  const [status, setStatus] = useState(currentFilters.status);

  /**
   * Update URL with new filter parameters
   */
  const updateFilters = (newFilters: {
    patientName?: string;
    status?: ClaimStatus | '';
  }) => {
    const params = new URLSearchParams(searchParams.toString());
    
    // Update filter params
    if (newFilters.patientName !== undefined) {
      if (newFilters.patientName.trim()) {
        params.set('patientName', newFilters.patientName.trim());
      } else {
        params.delete('patientName');
      }
    }
    
    if (newFilters.status !== undefined) {
      if (newFilters.status) {
        params.set('status', newFilters.status);
      } else {
        params.delete('status');
      }
    }
    
    // Reset to page 1 when filtering
    params.set('page', '1');
    
    startTransition(() => {
      router.push(`/?${params.toString()}`);
    });
  };

  /**
   * Handle patient name search
   */
  const handlePatientSearch = (e: React.FormEvent) => {
    e.preventDefault();
    updateFilters({ patientName });
  };

  /**
   * Handle status filter change
   */
  const handleStatusChange = (newStatus: ClaimStatus | '') => {
    setStatus(newStatus);
    updateFilters({ status: newStatus });
  };

  /**
   * Clear all filters
   */
  const clearFilters = () => {
    setPatientName('');
    setStatus('');
    
    const params = new URLSearchParams(searchParams.toString());
    params.delete('patientName');
    params.delete('status');
    params.set('page', '1');
    
    startTransition(() => {
      router.push(`/?${params.toString()}`);
    });
  };

  return (
    <div className="flex gap-4 items-center">
      {/* Patient Name Search */}
      <form onSubmit={handlePatientSearch} className="flex gap-2">
        <input
          type="text"
          placeholder="Search by patient name..."
          value={patientName}
          onChange={(e) => setPatientName(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled={isPending}
        />
        <button
          type="submit"
          disabled={isPending}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Search
        </button>
      </form>

      {/* Status Filter */}
      <select
        value={status}
        onChange={(e) => handleStatusChange(e.target.value as ClaimStatus | '')}
        disabled={isPending}
        className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
      >
        <option value="">All Statuses</option>
        <option value="PAID">Paid</option>
        <option value="DENIED">Denied</option>
        <option value="PENDING">Pending</option>
      </select>

      {/* Clear Filters Button */}
      {(currentFilters.patientName || currentFilters.status) && (
        <button
          onClick={clearFilters}
          disabled={isPending}
          className="px-3 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Clear Filters
        </button>
      )}

      {/* Loading indicator */}
      {isPending && (
        <div className="text-sm text-gray-500">
          Updating...
        </div>
      )}
    </div>
  );
}

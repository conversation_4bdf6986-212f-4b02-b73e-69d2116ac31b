import type { Metadata } from "next";
import localFont from "next/font/local";
import { Geist_Mono } from "next/font/google";
import "./globals.css";

const polySans = localFont({
  src: [
    {
      path: "../fonts/PolySans-Slim.otf",
      weight: "400",
      style: "normal",
    },
    {
      path: "../fonts/PolySans-Neutral.otf",
      weight: "500",
      style: "normal",
    },
    {
      path: "../fonts/PolySans-Median.otf",
      weight: "600",
      style: "normal",
    },
    {
      path: "../fonts/PolySans-Bulky.otf",
      weight: "700",
      style: "normal",
    },
  ],
  variable: "--font-poly-sans",
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "DENTL Insurance Claims Listings",
  description: "Insurance claims listings for DENTL",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${polySans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
